#!/bin/bash
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Logging functions
log_phase() { echo -e "\n${PURPLE}🔥 PHASE $1: $2${NC}"; }
log_test() { echo -e "${BLUE}🧪 $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warn() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }
log_info() { echo -e "${CYAN}ℹ️  $1${NC}"; }

# Test results tracking
TESTS_PASSED=0
TESTS_FAILED=0
PHASE_RESULTS=()

# Function to run test and track results
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    log_test "Running: $test_name"
    
    if eval "$test_command" > /tmp/test_output 2>&1; then
        log_success "$test_name"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        return 0
    else
        log_error "$test_name"
        echo "Error output:"
        cat /tmp/test_output | head -20
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
}

# Function to check file exists and show stats
check_output_file() {
    local file="$1"
    local description="$2"
    
    if [[ -f "$file" ]]; then
        local size=$(du -h "$file" | cut -f1)
        local lines=$(wc -l < "$file" 2>/dev/null || echo "N/A")
        log_success "$description exists ($size, $lines lines)"
        return 0
    else
        log_error "$description missing: $file"
        return 1
    fi
}

# Start comprehensive testing
echo -e "${PURPLE}🚀 COMPREHENSIVE M1.1 + M1.2 IMPLEMENTATION TESTING${NC}"
echo -e "${CYAN}Testing both Static Code Parser (M1.1) and Bidirectional Sync (M1.2)${NC}"
echo "=================================================================="

# Phase 1: Foundation Validation
log_phase "1" "Foundation Validation"

cd code

run_test "Package build system" "pnpm build"
run_test "Unit test suite" "pnpm test"
run_test "Linting checks" "pnpm lint"

PHASE_RESULTS+=("Phase 1: Foundation - Passed: $TESTS_PASSED, Failed: $TESTS_FAILED")

# Phase 2: M1.1 Static Code Parser Testing
log_phase "2" "M1.1 Static Code Parser Testing"

run_test "M1.1: Parse test fixtures" "pnpm run build-kg --code packages/code-parser-lib/tests/fixtures --dry-run ../docs/tech-specs"

# Check M1.1 outputs
if check_output_file "kg.jsonld" "Knowledge graph JSON-LD"; then
    # Analyze knowledge graph content
    if command -v jq &> /dev/null; then
        TOTAL_NODES=$(jq '.["@graph"] | length' kg.jsonld 2>/dev/null || echo "0")
        FUNCTIONS=$(jq '.["@graph"] | map(select(.["@type"] == "function")) | length' kg.jsonld 2>/dev/null || echo "0")
        MILESTONES=$(jq '.["@graph"] | map(select(.["@type"] == "Milestone")) | length' kg.jsonld 2>/dev/null || echo "0")
        
        log_info "Knowledge Graph Stats: $TOTAL_NODES total nodes, $FUNCTIONS functions, $MILESTONES milestones"
        
        if [[ $TOTAL_NODES -gt 10 ]]; then
            log_success "M1.1: Good knowledge graph content"
            TESTS_PASSED=$((TESTS_PASSED + 1))
        else
            log_warn "M1.1: Low knowledge graph content"
            TESTS_FAILED=$((TESTS_FAILED + 1))
        fi
    fi
fi

check_output_file "kg.yaml" "Knowledge graph YAML"

PHASE_RESULTS+=("Phase 2: M1.1 - Passed: $TESTS_PASSED, Failed: $TESTS_FAILED")

# Phase 3: M1.2 Bidirectional Sync Testing
log_phase "3" "M1.2 Bidirectional Sync Testing"

# Create test scenario
log_test "Creating test annotations for M1.2"
cat > test-m1.2-annotations.ts << 'EOF'
/**
 * @implements milestone-M1.2#TestAnnotationParser
 * Test function for M1.2 validation
 */
function testAnnotationParsing() {
    return "test";
}

/**
 * @implements milestone-M1.2#TestValidationEngine  
 * Another test function for validation
 */
function testValidation() {
    return "validation";
}

/**
 * @implements milestone-M1.1#TestCodeParser
 * Test function for M1.1 validation
 */
function testCodeParsing() {
    return "parsing";
}
EOF

# Test incremental sync
run_test "M1.2: Incremental sync with test annotations" "pnpm run sync-kg --since HEAD~1 ../docs/tech-specs"

# Check M1.2 outputs
if check_output_file "kg-changes.json" "Sync changes report"; then
    if command -v jq &> /dev/null; then
        NODES_ADDED=$(jq '.summary.nodesAdded' kg-changes.json 2>/dev/null || echo "0")
        EDGES_ADDED=$(jq '.summary.edgesAdded' kg-changes.json 2>/dev/null || echo "0")
        NODES_UPDATED=$(jq '.summary.nodesUpdated' kg-changes.json 2>/dev/null || echo "0")
        
        log_info "Sync Results: +$NODES_ADDED nodes, +$EDGES_ADDED edges, ~$NODES_UPDATED updated"
        
        if [[ $NODES_ADDED -gt 0 || $EDGES_ADDED -gt 0 ]]; then
            log_success "M1.2: Incremental sync detected changes"
            TESTS_PASSED=$((TESTS_PASSED + 1))
        else
            log_warn "M1.2: No changes detected in incremental sync"
            TESTS_FAILED=$((TESTS_FAILED + 1))
        fi
    fi
fi

# Clean up test file
rm -f test-m1.2-annotations.ts

PHASE_RESULTS+=("Phase 3: M1.2 - Passed: $TESTS_PASSED, Failed: $TESTS_FAILED")

# Phase 4: Error Handling Testing
log_phase "4" "Error Handling & Edge Cases"

# Test malformed annotations
cat > temp-bad-annotations.ts << 'EOF'
/**
 * @implements milestone-M1.2  // Missing component name
 */
function badAnnotation1() {}

/**
 * @implements M1.2#BadFormat  // Missing milestone- prefix
 */
function badAnnotation2() {}
EOF

log_test "Testing error detection with malformed annotations"
if pnpm run sync-kg --since HEAD~1 ../docs/tech-specs > /tmp/error_test 2>&1; then
    log_warn "Expected errors but sync succeeded - check error handling"
    TESTS_FAILED=$((TESTS_FAILED + 1))
else
    EXIT_CODE=$?
    log_success "Sync failed as expected with exit code: $EXIT_CODE"
    
    # Check for proper error reporting
    if grep -q "ERROR\|WARNING" /tmp/error_test; then
        log_success "Proper error messages generated"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        log_warn "No error messages found in output"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
fi

rm -f temp-bad-annotations.ts

PHASE_RESULTS+=("Phase 4: Error Handling - Passed: $TESTS_PASSED, Failed: $TESTS_FAILED")

# Phase 5: Performance Testing
log_phase "5" "Performance Validation"

log_test "Measuring full repository scan performance"
START_TIME=$(date +%s.%N)
pnpm run build-kg --code packages/ ../docs/tech-specs > /dev/null 2>&1
FULL_SCAN_TIME=$(echo "$(date +%s.%N) - $START_TIME" | bc 2>/dev/null || echo "N/A")

# Create a small change for incremental test
echo "// Performance test change $(date)" >> packages/shared/src/index.ts

log_test "Measuring incremental scan performance"
START_TIME=$(date +%s.%N)
pnpm run sync-kg --since HEAD~1 ../docs/tech-specs > /dev/null 2>&1
INCREMENTAL_TIME=$(echo "$(date +%s.%N) - $START_TIME" | bc 2>/dev/null || echo "N/A")

log_info "Full scan time: ${FULL_SCAN_TIME}s"
log_info "Incremental scan time: ${INCREMENTAL_TIME}s"

# Reset the test change
git checkout -- packages/shared/src/index.ts 2>/dev/null || true

if [[ "$FULL_SCAN_TIME" != "N/A" && "$INCREMENTAL_TIME" != "N/A" ]]; then
    IMPROVEMENT=$(echo "scale=1; ($FULL_SCAN_TIME - $INCREMENTAL_TIME) / $FULL_SCAN_TIME * 100" | bc 2>/dev/null || echo "N/A")
    if [[ "$IMPROVEMENT" != "N/A" ]]; then
        log_info "Performance improvement: ${IMPROVEMENT}%"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    fi
else
    log_warn "Could not calculate performance metrics"
    TESTS_FAILED=$((TESTS_FAILED + 1))
fi

PHASE_RESULTS+=("Phase 5: Performance - Passed: $TESTS_PASSED, Failed: $TESTS_FAILED")

# Phase 6: Official Acceptance Tests
log_phase "6" "Official Acceptance Tests"

cd ..

run_test "M1.2 Official Acceptance Tests" "bash docs/scripts/acceptance/m1.2-acceptance.sh"
run_test "M1.1 Official Acceptance Tests" "bash docs/scripts/acceptance/milestone-M1.1.sh"

PHASE_RESULTS+=("Phase 6: Acceptance Tests - Passed: $TESTS_PASSED, Failed: $TESTS_FAILED")

# Phase 7: Final Integration Check
log_phase "7" "Final Integration Validation"

cd code

# Generate final comprehensive knowledge graph
run_test "Final comprehensive knowledge graph generation" "pnpm run build-kg --code packages/ ../docs/tech-specs"

# Final statistics
if [[ -f kg.jsonld ]] && command -v jq &> /dev/null; then
    echo -e "\n${CYAN}📊 FINAL KNOWLEDGE GRAPH STATISTICS:${NC}"
    echo "======================================"
    
    TOTAL_NODES=$(jq '.["@graph"] | length' kg.jsonld)
    MILESTONES=$(jq '.["@graph"] | map(select(.["@type"] == "Milestone")) | length' kg.jsonld)
    ADRS=$(jq '.["@graph"] | map(select(.["@type"] == "ArchitecturalDecision")) | length' kg.jsonld)
    FUNCTIONS=$(jq '.["@graph"] | map(select(.["@type"] == "function")) | length' kg.jsonld)
    CALL_EDGES=$(jq '.["@graph"] | map(select(.["@type"] == "workflow_calls")) | length' kg.jsonld)
    
    echo "Total nodes: $TOTAL_NODES"
    echo "Milestones: $MILESTONES"
    echo "ADRs: $ADRS"
    echo "Functions: $FUNCTIONS"
    echo "Call edges: $CALL_EDGES"
    
    if [[ $TOTAL_NODES -gt 30 ]]; then
        log_success "Rich knowledge graph generated"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        log_warn "Knowledge graph seems sparse"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
fi

PHASE_RESULTS+=("Phase 7: Final Integration - Passed: $TESTS_PASSED, Failed: $TESTS_FAILED")

# Final Results Summary
echo -e "\n${PURPLE}🎯 COMPREHENSIVE TEST RESULTS${NC}"
echo "=============================================="

for result in "${PHASE_RESULTS[@]}"; do
    echo "$result"
done

echo -e "\n${CYAN}📈 OVERALL SUMMARY:${NC}"
echo "Total tests passed: $TESTS_PASSED"
echo "Total tests failed: $TESTS_FAILED"
echo "Success rate: $(echo "scale=1; $TESTS_PASSED * 100 / ($TESTS_PASSED + $TESTS_FAILED)" | bc 2>/dev/null || echo "N/A")%"

if [[ $TESTS_FAILED -eq 0 ]]; then
    echo -e "\n${GREEN}🎉 ALL TESTS PASSED! Implementation is working correctly.${NC}"
    echo -e "${GREEN}✅ M1.1 Static Code Parser: Functional${NC}"
    echo -e "${GREEN}✅ M1.2 Bidirectional Sync: Functional${NC}"
    echo -e "${GREEN}✅ Integration: Working${NC}"
    echo -e "${GREEN}✅ Error Handling: Robust${NC}"
    echo -e "${GREEN}✅ Performance: Optimized${NC}"
elif [[ $TESTS_FAILED -lt 3 ]]; then
    echo -e "\n${YELLOW}⚠️  MOSTLY WORKING with minor issues.${NC}"
    echo -e "${YELLOW}Most functionality is working, but some edge cases need attention.${NC}"
else
    echo -e "\n${RED}❌ SIGNIFICANT ISSUES DETECTED${NC}"
    echo -e "${RED}Multiple test failures indicate problems that need investigation.${NC}"
fi

echo -e "\n${CYAN}📁 Generated Files:${NC}"
echo "- kg.jsonld (Knowledge graph in JSON-LD format)"
echo "- kg.yaml (Knowledge graph in YAML format)"  
echo "- kg-changes.json (Incremental sync changes)"
echo "- kg-viewer.html (Visual knowledge graph explorer)"

echo -e "\n${CYAN}🔍 Next Steps:${NC}"
echo "1. Review any failed tests above"
echo "2. Open kg-viewer.html in browser to explore knowledge graph"
echo "3. Check kg-changes.json for sync operation details"
echo "4. Run individual tests for detailed debugging if needed"

echo -e "\n${PURPLE}Testing completed!${NC}"
