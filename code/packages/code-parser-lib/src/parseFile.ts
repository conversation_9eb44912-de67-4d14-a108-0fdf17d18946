import Parser, { Language } from 'tree-sitter';
import Python from 'tree-sitter-python';
import JavaScript from 'tree-sitter-javascript';
// @ts-expect-error - tree-sitter-typescript doesn't have proper type declarations
import TypeScript from 'tree-sitter-typescript/bindings/node/typescript.js';
import { readFileSync, readdirSync, statSync } from 'fs';
import { join, extname } from 'path';
import { v4 as uuidv4 } from 'uuid';
import { createHash } from 'crypto';

export interface ParsedFunction {
  id: string;
  name: string;
  signature: string;
  file: string;
  lang: 'python' | 'javascript' | 'typescript';
  line_start: number;
  line_end: number;
}

export interface ParsedCall {
  id: string;
  caller: string;
  callee: string;
  file: string;
  line: number;
}

export interface CodeParseResult {
  functions: ParsedFunction[];
  calls: ParsedCall[];
  errors: CodeParseError[];
}

export interface CodeParseError {
  filePath: string;
  error: string;
  details?: string;
}

/**
 * Parse all code files in a directory recursively
 */
export function parseCodeDirectory(
  directoryPath: string,
  languages: string[] = ['py', 'js']
): CodeParseResult {
  const functions: ParsedFunction[] = [];
  const calls: ParsedCall[] = [];
  const errors: CodeParseError[] = [];

  try {
    const files = findCodeFiles(directoryPath, languages);

    for (const filePath of files) {
      try {
        const result = parseFile(filePath);
        functions.push(...result.functions);
        calls.push(...result.calls);
        errors.push(...result.errors);
      } catch (error) {
        errors.push({
          filePath,
          error: 'Failed to parse file',
          details: error instanceof Error ? error.message : String(error),
        });
      }
    }
  } catch (error) {
    errors.push({
      filePath: directoryPath,
      error: 'Failed to read directory',
      details: error instanceof Error ? error.message : String(error),
    });
  }

  return { functions, calls, errors };
}

/**
 * Parse a single code file
 */
export function parseFile(filePath: string): CodeParseResult {
  const functions: ParsedFunction[] = [];
  const calls: ParsedCall[] = [];
  const errors: CodeParseError[] = [];

  try {
    const content = readFileSync(filePath, 'utf-8');
    const lang = getLanguageFromFile(filePath);

    if (!lang) {
      errors.push({
        filePath,
        error: 'Unsupported file type',
        details: `File extension not supported: ${extname(filePath)}`,
      });
      return { functions, calls, errors };
    }

    const parser = new Parser();
    let language: Language;
    switch (lang) {
      case 'python':
        language = Python as Language;
        break;
      case 'javascript':
        language = JavaScript as Language;
        break;
      case 'typescript':
        language = TypeScript as Language;
        break;
      default:
        throw new Error(`Unsupported language: ${lang}`);
    }
    parser.setLanguage(language);

    const tree = parser.parse(content);
    const rootNode = tree.rootNode;

    // Extract functions
    const extractedFunctions = extractFunctions(rootNode, filePath, lang);
    functions.push(...extractedFunctions);

    // Extract calls
    const extractedCalls = extractCalls(rootNode, filePath);
    calls.push(...extractedCalls);
  } catch (error) {
    errors.push({
      filePath,
      error: 'Parse error',
      details: error instanceof Error ? error.message : String(error),
    });
  }

  return { functions, calls, errors };
}

/**
 * Find all code files in a directory recursively
 */
function findCodeFiles(directoryPath: string, languages: string[]): string[] {
  const files: string[] = [];
  const extensions = new Set(
    languages
      .map((lang) => {
        switch (lang) {
          case 'py':
            return '.py';
          case 'js':
            return ['.js', '.mjs', '.cjs'];
          case 'ts':
            return ['.ts', '.tsx'];
          default:
            return `.${lang}`;
        }
      })
      .flat()
  );

  function traverse(dir: string) {
    const entries = readdirSync(dir);

    for (const entry of entries) {
      const fullPath = join(dir, entry);
      const stat = statSync(fullPath);

      if (stat.isDirectory()) {
        // Skip node_modules and other common ignore patterns
        if (
          !['node_modules', '.git', 'dist', 'build', '__pycache__'].includes(
            entry
          )
        ) {
          traverse(fullPath);
        }
      } else if (stat.isFile()) {
        const ext = extname(fullPath);
        if (extensions.has(ext)) {
          files.push(fullPath);
        }
      }
    }
  }

  traverse(directoryPath);
  return files;
}

/**
 * Determine language from file extension
 */
function getLanguageFromFile(
  filePath: string
): 'python' | 'javascript' | 'typescript' | null {
  const ext = extname(filePath);

  switch (ext) {
    case '.py':
      return 'python';
    case '.js':
    case '.mjs':
    case '.cjs':
      return 'javascript';
    case '.ts':
    case '.tsx':
      return 'typescript';
    default:
      return null;
  }
}

/**
 * Extract function definitions from AST
 */
function extractFunctions(
  node: Parser.SyntaxNode,
  filePath: string,
  lang: 'python' | 'javascript' | 'typescript'
): ParsedFunction[] {
  const functions: ParsedFunction[] = [];

  function traverse(node: Parser.SyntaxNode) {
    // Python function definitions
    if (lang === 'python' && node.type === 'function_definition') {
      const nameNode = node.childForFieldName('name');
      const parametersNode = node.childForFieldName('parameters');

      if (nameNode) {
        const name = nameNode.text;
        const signature = parametersNode
          ? `${name}${parametersNode.text}`
          : name;
        const startLine = node.startPosition.row + 1;
        const endLine = node.endPosition.row + 1;

        const id = generateFunctionId(filePath, name, startLine);

        functions.push({
          id,
          name,
          signature,
          file: filePath,
          lang,
          line_start: startLine,
          line_end: endLine,
        });
      }
    }

    // JavaScript function declarations and expressions
    if (
      lang === 'javascript' &&
      (node.type === 'function_declaration' ||
        node.type === 'function_expression' ||
        node.type === 'arrow_function' ||
        node.type === 'method_definition')
    ) {
      let name = 'anonymous';
      let signature = '';

      if (node.type === 'function_declaration') {
        const nameNode = node.childForFieldName('name');
        const parametersNode = node.childForFieldName('parameters');

        if (nameNode) {
          name = nameNode.text;
          signature = parametersNode ? `${name}${parametersNode.text}` : name;
        }
      } else if (node.type === 'method_definition') {
        const nameNode = node.childForFieldName('name');
        const parametersNode = node.childForFieldName('parameters');

        if (nameNode) {
          name = nameNode.text;
          signature = parametersNode ? `${name}${parametersNode.text}` : name;
        }
      } else if (node.type === 'arrow_function') {
        const parametersNode =
          node.childForFieldName('parameters') ||
          node.childForFieldName('parameter');
        signature = parametersNode ? `(${parametersNode.text}) =>` : '() =>';
      }

      const startLine = node.startPosition.row + 1;
      const endLine = node.endPosition.row + 1;
      const id = generateFunctionId(filePath, name, startLine);

      functions.push({
        id,
        name,
        signature,
        file: filePath,
        lang,
        line_start: startLine,
        line_end: endLine,
      });
    }

    // TypeScript function declarations and expressions (similar to JavaScript but with type annotations)
    if (
      lang === 'typescript' &&
      (node.type === 'function_declaration' ||
        node.type === 'function_expression' ||
        node.type === 'arrow_function' ||
        node.type === 'method_definition' ||
        node.type === 'function_signature')
    ) {
      let name = 'anonymous';
      let signature = '';

      if (node.type === 'function_declaration') {
        const nameNode = node.childForFieldName('name');
        const parametersNode = node.childForFieldName('parameters');

        if (nameNode) {
          name = nameNode.text;
          signature = parametersNode ? `${name}${parametersNode.text}` : name;
        }
      } else if (node.type === 'method_definition') {
        const nameNode = node.childForFieldName('name');
        const parametersNode = node.childForFieldName('parameters');

        if (nameNode) {
          name = nameNode.text;
          signature = parametersNode ? `${name}${parametersNode.text}` : name;
        }
      } else if (node.type === 'arrow_function') {
        const parametersNode =
          node.childForFieldName('parameters') ||
          node.childForFieldName('parameter');
        signature = parametersNode ? `(${parametersNode.text}) =>` : '() =>';
      }

      const startLine = node.startPosition.row + 1;
      const endLine = node.endPosition.row + 1;
      const id = generateFunctionId(filePath, name, startLine);

      functions.push({
        id,
        name,
        signature,
        file: filePath,
        lang,
        line_start: startLine,
        line_end: endLine,
      });
    }

    // Recursively traverse child nodes
    for (let i = 0; i < node.childCount; i++) {
      const child = node.child(i);
      if (child) {
        traverse(child);
      }
    }
  }

  traverse(node);
  return functions;
}

/**
 * Extract function calls from AST
 */
function extractCalls(node: Parser.SyntaxNode, filePath: string): ParsedCall[] {
  const calls: ParsedCall[] = [];

  function traverse(node: Parser.SyntaxNode) {
    // Function calls
    if (node.type === 'call' || node.type === 'call_expression') {
      const functionNode = node.childForFieldName('function');

      if (functionNode) {
        const callee = functionNode.text;
        const line = node.startPosition.row + 1;
        const id = uuidv4();

        calls.push({
          id,
          caller: 'unknown', // Will be resolved in callGraph.ts
          callee,
          file: filePath,
          line,
        });
      }
    }

    // Recursively traverse child nodes
    for (let i = 0; i < node.childCount; i++) {
      const child = node.child(i);
      if (child) {
        traverse(child);
      }
    }
  }

  traverse(node);
  return calls;
}

/**
 * Generate deterministic function ID
 */
function generateFunctionId(
  filePath: string,
  name: string,
  startLine: number
): string {
  const hash = createHash('md5')
    .update(`${filePath}:${name}:${startLine}`)
    .digest('hex')
    .substring(0, 8);

  return `function:${hash}`;
}
