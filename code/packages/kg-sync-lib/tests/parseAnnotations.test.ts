/**
 * @fileoverview Tests for annotation parsing functionality
 */

import { parseAnnotations } from '../src/parseAnnotations.js';

describe('parseAnnotations', () => {
  it('should parse valid @implements annotation', () => {
    const fileContent = `
/**
 * @implements milestone-M1.2#AnnotationParser
 */
export function parseAnnotations() {
  return [];
}
`;

    const result = parseAnnotations(fileContent, 'src/parser.ts');

    expect(result).toHaveLength(1);
    expect(result[0]).toMatchObject({
      milestoneId: 'M1.2',
      componentName: 'AnnotationParser',
      functionName: 'parseAnnotations',
      filePath: 'src/parser.ts',
      confidence: 0.9, // Reduced due to file name mismatch warning
    });
    expect(result[0]!.errors).toHaveLength(1);
    expect(result[0]!.errors[0]!.severity).toBe('warning');
  });

  it('should parse multiple @implements annotations', () => {
    const fileContent = `
/**
 * @implements milestone-M1.2#AnnotationParser
 * @implements milestone-M1.2#ValidationEngine
 */
export function complexFunction() {
  return [];
}
`;

    const result = parseAnnotations(fileContent, 'src/complex.ts');

    expect(result).toHaveLength(2);
    expect(result[0]!.componentName).toBe('AnnotationParser');
    expect(result[1]!.componentName).toBe('ValidationEngine');
    expect(result[0]!.functionName).toBe('complexFunction');
    expect(result[1]!.functionName).toBe('complexFunction');
  });

  it('should handle class annotations', () => {
    const fileContent = `
/**
 * @implements milestone-M1.2#ParserClass
 */
export class AnnotationParser {
  parse() {}
}
`;

    const result = parseAnnotations(fileContent, 'src/class.ts');

    expect(result).toHaveLength(1);
    expect(result[0]).toMatchObject({
      milestoneId: 'M1.2',
      componentName: 'ParserClass',
      functionName: 'AnnotationParser',
      confidence: 0.9,
    });
  });

  it('should handle method annotations', () => {
    const fileContent = `
class Parser {
  /**
   * @implements milestone-M1.2#ParseMethod
   */
  parseMethod() {
    return [];
  }
}
`;

    const result = parseAnnotations(fileContent, 'src/method.ts');

    expect(result).toHaveLength(1);
    expect(result[0]).toMatchObject({
      milestoneId: 'M1.2',
      componentName: 'ParseMethod',
      functionName: 'Parser',
      confidence: 0.9,
    });
  });

  it('should handle arrow function annotations', () => {
    const fileContent = `
/**
 * @implements milestone-M1.2#ArrowFunction
 */
const parseData = () => {
  return [];
};
`;

    const result = parseAnnotations(fileContent, 'src/arrow.ts');

    expect(result).toHaveLength(1);
    expect(result[0]).toMatchObject({
      milestoneId: 'M1.2',
      componentName: 'ArrowFunction',
      functionName: 'parseData',
      confidence: 0.8,
    });
  });

  it('should validate milestone ID format', () => {
    const fileContent = `
/**
 * @implements M1.2#InvalidFormat
 */
function testFunction() {}
`;

    const result = parseAnnotations(fileContent, 'src/invalid.ts');

    expect(result).toHaveLength(1);
    expect(result[0]!.confidence).toBe(0.4);
    expect(result[0]!.errors).toHaveLength(2);
    expect(result[0]!.errors[0]!.message).toContain(
      'Missing "milestone-" prefix'
    );
  });

  it('should validate component name format', () => {
    const fileContent = `
/**
 * @implements milestone-M1.2#123Invalid
 */
function testFunction() {}
`;

    const result = parseAnnotations(fileContent, 'src/invalid.ts');

    expect(result).toHaveLength(1);
    expect(result[0]!.confidence).toBe(0.7);
    expect(result[0]!.errors).toHaveLength(1);
    expect(result[0]!.errors[0]!.message).toContain(
      'Invalid @implements format'
    );
  });

  it('should handle empty @implements tag', () => {
    const fileContent = `
/**
 * @implements
 */
function testFunction() {}
`;

    const result = parseAnnotations(fileContent, 'src/empty.ts');

    expect(result).toHaveLength(1);
    expect(result[0]!.confidence).toBe(0.7);
    expect(result[0]!.errors).toHaveLength(1); // Empty tag only
    expect(result[0]!.errors[0]!.message).toContain('empty');
  });

  it('should handle annotation without attached function', () => {
    const fileContent = `
/**
 * @implements milestone-M1.2#OrphanAnnotation
 */
// Just a comment, no function follows
`;

    const result = parseAnnotations(fileContent, 'src/orphan.ts');

    expect(result).toHaveLength(1);
    expect(result[0]!.confidence).toBe(0.7);
    expect(result[0]!.errors).toHaveLength(2);
    expect(result[0]!.errors[0]!.message).toContain(
      'not attached to a function'
    );
  });

  it('should handle malformed comments gracefully', () => {
    const fileContent = `
/**
 * @implements milestone-M1.2#ValidAnnotation
 */
function validFunction() {}

/* Malformed comment without proper JSDoc
 * @implements milestone-M1.2#ShouldBeIgnored
function invalidFunction() {}
`;

    const result = parseAnnotations(fileContent, 'src/malformed.ts');

    // Should only find the valid annotation
    expect(result).toHaveLength(1);
    expect(result[0]!.componentName).toBe('ValidAnnotation');
    expect(result[0]!.functionName).toBe('validFunction');
  });

  it('should handle nested function annotations', () => {
    const fileContent = `
/**
 * @implements milestone-M1.2#OuterFunction
 */
function outerFunction() {
  /**
   * @implements milestone-M1.2#InnerFunction
   */
  function innerFunction() {
    return true;
  }
  return innerFunction();
}
`;

    const result = parseAnnotations(fileContent, 'src/nested.ts');

    expect(result).toHaveLength(2);
    expect(result[0]!.componentName).toBe('OuterFunction');
    expect(result[0]!.functionName).toBe('outerFunction');
    expect(result[1]!.componentName).toBe('InnerFunction');
    expect(result[1]!.functionName).toBe('outerFunction');
  });

  it('should handle complex milestone IDs', () => {
    const fileContent = `
/**
 * @implements milestone-M1.2.3#ComplexComponent
 */
function complexMilestone() {}
`;

    const result = parseAnnotations(fileContent, 'src/complex.ts');

    expect(result).toHaveLength(1);
    expect(result[0]).toMatchObject({
      milestoneId: 'M1.2.3',
      componentName: 'ComplexComponent',
      confidence: 0.9,
    });
  });

  it('should return empty array for files without annotations', () => {
    const fileContent = `
function normalFunction() {
  return 'no annotations here';
}

class NormalClass {
  method() {}
}
`;

    const result = parseAnnotations(fileContent, 'src/normal.ts');

    expect(result).toHaveLength(0);
  });

  it('should handle parsing errors gracefully', () => {
    // Test with a file that has no valid comments but should not crash
    const fileContent = '/* unclosed comment';

    const result = parseAnnotations(fileContent, 'src/broken.ts');

    // Should return empty array for files without valid JSDoc comments
    expect(result).toHaveLength(0);
  });

  it('should handle comment parser throwing errors', () => {
    // Mock comment-parser to throw an error
    const originalParse = require('comment-parser').parse;
    require('comment-parser').parse = jest.fn().mockImplementation(() => {
      throw new Error('Comment parser failed');
    });

    const fileContent = '/** @implements milestone-M1.2#TestComponent */ function test() {}';
    const result = parseAnnotations(fileContent, 'src/error.ts');

    expect(result).toHaveLength(1);
    expect(result[0]!.confidence).toBeLessThanOrEqual(0.7); // Enhanced validation may adjust confidence
    expect(result[0]!.errors.length).toBeGreaterThan(0);
    expect(result[0]!.errors.some(e => e.message.includes('Failed to parse comments'))).toBe(true);

    // Restore original function
    require('comment-parser').parse = originalParse;
  });

  it('should handle non-Error objects thrown by comment parser', () => {
    // Mock comment-parser to throw a non-Error object
    const originalParse = require('comment-parser').parse;
    require('comment-parser').parse = jest.fn().mockImplementation(() => {
      throw 'String error';
    });

    const fileContent = '/** @implements milestone-M1.2#TestComponent */ function test() {}';
    const result = parseAnnotations(fileContent, 'src/error.ts');

    expect(result).toHaveLength(1);
    expect(result[0]!.confidence).toBeLessThanOrEqual(0.7); // Enhanced validation may adjust confidence
    expect(result[0]!.errors.length).toBeGreaterThan(0);
    expect(result[0]!.errors.some(e => e.message.includes('Failed to parse comments: String error'))).toBe(true);

    // Restore original function
    require('comment-parser').parse = originalParse;
  });

  it('should handle annotations with tag.name instead of tag.description', () => {
    // Mock comment-parser to return tag with name property
    const originalParse = require('comment-parser').parse;
    require('comment-parser').parse = jest.fn().mockReturnValue([
      {
        line: 1,
        tags: [
          {
            tag: 'implements',
            name: 'milestone-M1.2#TestComponent',
            description: '',
          },
        ],
      },
    ]);

    const fileContent = '/** @implements milestone-M1.2#TestComponent */ function test() {}';
    const result = parseAnnotations(fileContent, 'src/test.ts');

    expect(result).toHaveLength(1);
    expect(result[0]!.milestoneId).toBe('M1.2');
    expect(result[0]!.componentName).toBe('TestComponent');

    // Restore original function
    require('comment-parser').parse = originalParse;
  });

  it('should handle annotations with tag.description instead of tag.name', () => {
    // Mock comment-parser to return tag with description property
    const originalParse = require('comment-parser').parse;
    require('comment-parser').parse = jest.fn().mockReturnValue([
      {
        line: 1,
        tags: [
          {
            tag: 'implements',
            name: '',
            description: 'milestone-M1.2#TestComponent',
          },
        ],
      },
    ]);

    const fileContent = '/** @implements milestone-M1.2#TestComponent */ function test() {}';
    const result = parseAnnotations(fileContent, 'src/test.ts');

    expect(result).toHaveLength(1);
    expect(result[0]!.milestoneId).toBe('M1.2');
    expect(result[0]!.componentName).toBe('TestComponent');

    // Restore original function
    require('comment-parser').parse = originalParse;
  });
});
