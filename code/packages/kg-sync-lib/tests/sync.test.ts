/**
 * @fileoverview Tests for sync orchestration functionality
 */

import { syncKnowledgeGraph } from '../src/sync.js';
import { diffGit } from '../src/diffGit.js';
import { parseAnnotations } from '../src/parseAnnotations.js';
import { updateGraph } from '../src/updateGraph.js';
import { calculateCoverage } from '../src/confidence.js';
import { readFileSync } from 'fs';

// Mock all dependencies
jest.mock('../src/diffGit.js');
jest.mock('../src/parseAnnotations.js');
jest.mock('../src/updateGraph.js');
jest.mock('../src/confidence.js');
jest.mock('fs');

const mockDiffGit = diffGit as jest.MockedFunction<typeof diffGit>;
const mockParseAnnotations = parseAnnotations as jest.MockedFunction<typeof parseAnnotations>;
const mockUpdateGraph = updateGraph as jest.MockedFunction<typeof updateGraph>;
const mockCalculateCoverage = calculateCoverage as jest.MockedFunction<typeof calculateCoverage>;
const mockReadFileSync = readFileSync as jest.MockedFunction<typeof readFileSync>;

describe('syncKnowledgeGraph', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Default successful mocks
    mockDiffGit.mockResolvedValue({
      changedFiles: ['src/test.ts'],
      addedFiles: ['src/new.ts'],
      deletedFiles: [],
      renamedFiles: [],
      errors: [],
    });

    mockReadFileSync.mockReturnValue('/** @implements milestone-M1.2#TestComponent */ function test() {}');

    mockParseAnnotations.mockReturnValue([
      {
        milestoneId: 'M1.2',
        componentName: 'TestComponent',
        functionName: 'test',
        filePath: 'src/test.ts',
        lineNumber: 1,
        confidence: 1.0,
        lastVerified: '2023-01-01T00:00:00.000Z',
        errors: [],
      },
    ]);

    mockUpdateGraph.mockReturnValue({
      nodesAdded: 1,
      nodesUpdated: 0,
      nodesMarkedStale: 0,
      edgesAdded: 1,
      edgesUpdated: 0,
      edgesMarkedStale: 0,
      coverageMetrics: [],
      errors: [],
    });

    mockCalculateCoverage.mockReturnValue({
      milestoneId: 'M1.2',
      totalComponents: 2,
      implementedComponents: 1,
      coverage: 0.5,
      confidence: 1.0,
      lastUpdated: '2023-01-01T00:00:00.000Z',
    });
  });

  it('should successfully orchestrate sync process', async () => {
    const result = await syncKnowledgeGraph('/test/directory', {
      verbose: true,
    });

    expect(result.success).toBe(true);
    expect(result.exitCode).toBe(0);
    expect(result.coverageMetrics).toHaveLength(1);
    expect(result.errors).toHaveLength(0);
    expect(result.warnings).toHaveLength(0);

    expect(mockDiffGit).toHaveBeenCalledWith({ verbose: true });
    expect(mockParseAnnotations).toHaveBeenCalledTimes(2); // For both changed files
    expect(mockUpdateGraph).toHaveBeenCalledWith(
      { nodes: [], edges: [] },
      expect.arrayContaining([
        expect.objectContaining({
          milestoneId: 'M1.2',
          componentName: 'TestComponent',
        }),
      ]),
      ['src/test.ts', 'src/new.ts']
    );
    expect(mockCalculateCoverage).toHaveBeenCalledWith('M1.2', [], 10);
  });

  it('should handle dry run mode', async () => {
    const result = await syncKnowledgeGraph('/test/directory', {
      dryRun: true,
      outputDir: '/custom/output',
    });

    expect(result.success).toBe(true);
    expect(mockUpdateGraph).toHaveBeenCalledWith(
      { nodes: [], edges: [] },
      expect.any(Array),
      ['src/test.ts', 'src/new.ts']
    );
  });

  it('should handle coverage breach (exit code 60)', async () => {
    mockCalculateCoverage.mockReturnValue({
      milestoneId: 'M1.2',
      totalComponents: 10,
      implementedComponents: 2,
      coverage: 0.2, // Below 0.5 threshold
      confidence: 1.0,
      lastUpdated: '2023-01-01T00:00:00.000Z',
    });

    const result = await syncKnowledgeGraph('/test/directory');

    expect(result.success).toBe(false);
    expect(result.exitCode).toBe(60);
    expect(result.errors).toHaveLength(1);
    expect(result.errors[0]!.message).toContain('Coverage below threshold');
  });

  it('should handle annotation parse errors (exit code 70)', async () => {
    mockParseAnnotations.mockReturnValue([
      {
        milestoneId: 'M1.2',
        componentName: 'TestComponent',
        functionName: 'test',
        filePath: 'src/test.ts',
        lineNumber: 1,
        confidence: 0.5,
        lastVerified: '2023-01-01T00:00:00.000Z',
        errors: [
          {
            line: 1,
            column: 1,
            message: 'Invalid annotation format',
            severity: 'error',
            suggestion: 'Fix the annotation',
          },
        ],
      },
    ]);

    const result = await syncKnowledgeGraph('/test/directory');

    expect(result.success).toBe(false);
    expect(result.exitCode).toBe(70);
  });

  it('should handle file read errors gracefully', async () => {
    mockReadFileSync.mockImplementation((filePath) => {
      if (filePath.toString().includes('src/test.ts')) {
        throw new Error('File not found');
      }
      return '/** @implements milestone-M1.2#TestComponent */ function test() {}';
    });

    const result = await syncKnowledgeGraph('/test/directory');

    expect(result.success).toBe(true); // Should continue despite file read error
    expect(result.warnings).toHaveLength(1);
    expect(result.warnings[0]!.message).toContain('Failed to read file');
  });

  it('should handle coverage calculation errors', async () => {
    mockCalculateCoverage.mockImplementation(() => {
      throw new Error('Coverage calculation failed');
    });

    const result = await syncKnowledgeGraph('/test/directory');

    expect(result.success).toBe(true); // Should continue despite coverage error
    expect(result.warnings).toHaveLength(1);
    expect(result.warnings[0]!.message).toContain('Failed to calculate coverage');
  });

  it('should handle unexpected errors (exit code 1)', async () => {
    mockDiffGit.mockRejectedValue(new Error('Git operation failed'));

    const result = await syncKnowledgeGraph('/test/directory');

    expect(result.success).toBe(false);
    expect(result.exitCode).toBe(1);
    expect(result.errors).toHaveLength(1);
    expect(result.errors[0]!.message).toContain('Sync failed');
  });

  it('should handle no changed files', async () => {
    mockDiffGit.mockResolvedValue({
      changedFiles: [],
      addedFiles: [],
      deletedFiles: [],
      renamedFiles: [],
      errors: [],
    });

    const result = await syncKnowledgeGraph('/test/directory');

    expect(result.success).toBe(true);
    expect(result.exitCode).toBe(0);
    expect(result.coverageMetrics).toHaveLength(0);
    expect(mockParseAnnotations).not.toHaveBeenCalled();
  });

  it('should handle multiple milestones', async () => {
    mockParseAnnotations.mockReturnValue([
      {
        milestoneId: 'M1.2',
        componentName: 'TestComponent1',
        functionName: 'test1',
        filePath: 'src/test1.ts',
        lineNumber: 1,
        confidence: 1.0,
        lastVerified: '2023-01-01T00:00:00.000Z',
        errors: [],
      },
      {
        milestoneId: 'M1.3',
        componentName: 'TestComponent2',
        functionName: 'test2',
        filePath: 'src/test2.ts',
        lineNumber: 1,
        confidence: 1.0,
        lastVerified: '2023-01-01T00:00:00.000Z',
        errors: [],
      },
    ]);

    const result = await syncKnowledgeGraph('/test/directory');

    expect(result.success).toBe(true);
    expect(result.coverageMetrics).toHaveLength(2);
    expect(mockCalculateCoverage).toHaveBeenCalledTimes(2);
    expect(mockCalculateCoverage).toHaveBeenCalledWith('M1.2', [], 10);
    expect(mockCalculateCoverage).toHaveBeenCalledWith('M1.3', [], 10);
  });

  it('should handle empty directory parameter', async () => {
    const result = await syncKnowledgeGraph('');

    expect(result.success).toBe(true);
    expect(mockDiffGit).toHaveBeenCalled();
  });

  it('should handle verbose logging', async () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

    await syncKnowledgeGraph('/test/directory', { verbose: true });

    expect(consoleSpy).toHaveBeenCalledWith('Starting knowledge graph sync for directory: /test/directory');
    expect(consoleSpy).toHaveBeenCalledWith('Found 2 changed files');
    expect(consoleSpy).toHaveBeenCalledWith('Parsed 2 annotations');
    expect(consoleSpy).toHaveBeenCalledWith('Sync completed with exit code: 0');

    consoleSpy.mockRestore();
  });
});
