/**
 * @fileoverview JSDoc annotation parsing with comment-parser
 * @implements milestone-M1.2#AnnotationParser
 */

import { parse as parseComments } from 'comment-parser';
import type { Annotation, ParseError } from './types.js';
import { validateAnnotations, validateAnnotationFormat } from './validation.js';

// Type definitions for comment-parser library
interface CommentTag {
  tag: string;
  name?: string;
  description?: string;
}

interface CommentBlock {
  line?: number;
  end?: { line?: number; };
  tags: CommentTag[];
}

// Regex pattern for validating milestone ID and component name
const IMPLEMENTS_PATTERN =
  /^milestone-(M\d+(?:\.\d+)*)#([A-Za-z_][A-Za-z0-9_]*)$/;

/**
 * Parse @implements annotations from source code
 * @implements milestone-M1.2#AnnotationParser
 * @param fileContent - Source code content
 * @param filePath - Path to the source file
 * @returns Array of parsed annotations
 */
export function parseAnnotations(
  fileContent: string,
  filePath: string
): Annotation[] {
  const annotations: Annotation[] = [];
  const currentTimestamp = new Date().toISOString();

  try {
    // Parse all JSDoc comments from the file
    const comments = parseComments(fileContent, {
      spacing: 'preserve',
    });

    // Process each comment block
    for (const comment of comments) {
      // Find @implements tags in this comment
      const implementsTags = comment.tags.filter(
        (tag) => tag.tag === 'implements'
      );

      for (const tag of implementsTags) {
        const annotation = parseImplementsTag(
          tag,
          comment,
          filePath,
          fileContent,
          currentTimestamp
        );
        if (annotation) {
          annotations.push(annotation);
        }
      }
    }
  } catch (error) {
    // If comment parsing fails entirely, create an annotation with error
    const errorAnnotation: Annotation = {
      milestoneId: '',
      componentName: '',
      functionName: '',
      filePath,
      lineNumber: 1,
      confidence: 0,
      lastVerified: currentTimestamp,
      errors: [
        {
          line: 1,
          column: 1,
          message: `Failed to parse comments: ${error instanceof Error ? error.message : String(error)
            }`,
          severity: 'error',
          suggestion: 'Check for malformed JSDoc comments',
        },
      ],
    };
    annotations.push(errorAnnotation);
  }

  // Apply enhanced validation rules
  return validateAnnotations(annotations);
}

/**
 * Parse a single @implements tag and create an annotation
 * @param tag - The @implements tag from comment-parser
 * @param comment - The full comment block
 * @param filePath - Path to the source file
 * @param fileContent - Full file content for context
 * @param timestamp - Current timestamp
 * @returns Parsed annotation or null if invalid
 */
function parseImplementsTag(
  tag: CommentTag,
  comment: CommentBlock,
  filePath: string,
  fileContent: string,
  timestamp: string
): Annotation | null {
  const errors: ParseError[] = [];
  const lineNumber = comment.line || 1;

  // Extract the annotation value (e.g., "milestone-M1.2#AnnotationParser")
  const annotationValue = tag.name || tag.description || '';

  // Use enhanced format validation
  const formatErrors = validateAnnotationFormat(annotationValue, lineNumber);
  errors.push(...formatErrors);

  // Validate the annotation format with regex
  const match = IMPLEMENTS_PATTERN.exec(annotationValue.trim());
  if (!match && annotationValue.trim()) {
    errors.push({
      line: lineNumber,
      column: 1,
      message: `Invalid @implements format: "${annotationValue}"`,
      severity: 'error',
      suggestion: 'Use format: @implements milestone-M1.2#ComponentName',
    });
  }

  const milestoneId = match ? match[1] || '' : '';
  const componentName = match ? match[2] || '' : '';

  // Find the function/class/method this annotation is attached to
  const functionName = findAttachedFunction(comment, fileContent, lineNumber);
  if (!functionName) {
    errors.push({
      line: lineNumber,
      column: 1,
      message:
        '@implements annotation not attached to a function, class, or method',
      severity: 'error',
      suggestion:
        'Place the annotation directly above a function, class, or method declaration',
    });
  }

  // Calculate confidence based on errors
  const confidence = errors.some((e) => e.severity === 'error') ? 0.5 : 1.0;

  return {
    milestoneId,
    componentName,
    functionName: functionName || '',
    filePath,
    lineNumber,
    confidence,
    lastVerified: timestamp,
    errors,
  };
}

/**
 * Find the function, class, or method that follows a comment block
 * @param comment - The comment block from comment-parser
 * @param fileContent - Full file content
 * @param commentLineNumber - Line number of the comment
 * @returns Function/class/method name or null if not found
 */
function findAttachedFunction(
  comment: CommentBlock,
  fileContent: string,
  commentLineNumber: number
): string | null {
  const lines = fileContent.split('\n');

  // Start looking after the comment block ends
  const startLine = comment.end?.line || commentLineNumber;

  // Look for function/class/method declarations in the next few lines
  for (let i = startLine; i < Math.min(startLine + 10, lines.length); i++) {
    const line = lines[i]?.trim();
    if (!line || line.startsWith('//') || line.startsWith('/*')) {
      continue; // Skip empty lines and comments
    }

    const functionName = extractFunctionName(line);
    if (functionName) {
      return functionName;
    }
  }

  return null;
}

/**
 * Extract function/class/method name from a line of code
 * @param line - Line of code to analyze
 * @returns Function name or null if not a function declaration
 */
function extractFunctionName(line: string): string | null {
  // Function declarations: function name(), const name = function(), const name = () =>
  const functionPatterns = [
    /^(?:export\s+)?(?:async\s+)?function\s+([A-Za-z_][A-Za-z0-9_]*)/,
    /^(?:export\s+)?const\s+([A-Za-z_][A-Za-z0-9_]*)\s*=\s*(?:async\s+)?(?:function|\()/,
    /^(?:export\s+)?let\s+([A-Za-z_][A-Za-z0-9_]*)\s*=\s*(?:async\s+)?(?:function|\()/,
    /^(?:export\s+)?var\s+([A-Za-z_][A-Za-z0-9_]*)\s*=\s*(?:async\s+)?(?:function|\()/,
  ];

  // Class declarations: class Name
  const classPattern =
    /^(?:export\s+)?(?:abstract\s+)?class\s+([A-Za-z_][A-Za-z0-9_]*)/;

  // Method declarations: methodName(), async methodName(), public methodName()
  const methodPatterns = [
    /^(?:public|private|protected|static)?\s*(?:async\s+)?([A-Za-z_][A-Za-z0-9_]*)\s*\(/,
    /^\s*([A-Za-z_][A-Za-z0-9_]*)\s*\(/,
  ];

  // Try function patterns first (these are more specific)
  for (const pattern of functionPatterns) {
    const match = pattern.exec(line);
    if (match && match[1]) {
      return match[1];
    }
  }

  // Try method patterns (for methods inside classes)
  if (
    line.includes('(') &&
    !line.includes('=') &&
    !line.includes('function') &&
    !line.includes('class')
  ) {
    for (const pattern of methodPatterns) {
      const match = pattern.exec(line);
      if (match && match[1]) {
        return match[1];
      }
    }
  }

  // Try class pattern last (least specific)
  const classMatch = classPattern.exec(line);
  if (classMatch && classMatch[1]) {
    return classMatch[1];
  }

  return null;
}
