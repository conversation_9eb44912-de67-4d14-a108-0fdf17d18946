import { buildKnowledgeGraph } from './index.js';
import {
  parseCodeDirectory,
  extractCallGraph,
} from '@workflow-mapper/code-parser-lib';
import { writeFileSync, mkdirSync, rmSync } from 'fs';
import { join } from 'path';

// Type definitions for JSON-LD structure
interface JsonLdNode {
  '@type': string;
  '@id'?: string;
  name?: string;
  [key: string]: unknown;
}

interface JsonLdGraph {
  '@context': unknown;
  '@graph': JsonLdNode[];
}

describe('Knowledge Graph Integration', () => {
  const testDir = join(__dirname, '../test-integration');
  const specsDir = join(testDir, 'specs');
  const codeDir = join(testDir, 'code');

  beforeEach(() => {
    // Create test directories
    mkdirSync(testDir, { recursive: true });
    mkdirSync(specsDir, { recursive: true });
    mkdirSync(codeDir, { recursive: true });
  });

  afterEach(() => {
    // Clean up test directory
    rmSync(testDir, { recursive: true, force: true });
  });

  describe('buildKnowledgeGraphWithCode', () => {
    it('should integrate code parsing results into knowledge graph', async () => {
      // Create test spec file
      const specContent = `---
title: "Test Spec"
description: "Test specification"
version: "1.0.0"
status: "Draft"
---

# Test Specification

This is a test specification.
`;
      writeFileSync(join(specsDir, 'test-spec.mdx'), specContent);

      // Create test code files
      const pythonCode = `
def calculate(x, y):
    """Calculate sum of two numbers."""
    return helper(x) + y

def helper(value):
    return value * 2
`;
      const jsCode = `
function process(data) {
    return transform(data);
}

function transform(input) {
    return input.toUpperCase();
}
`;
      writeFileSync(join(codeDir, 'math.py'), pythonCode);
      writeFileSync(join(codeDir, 'utils.js'), jsCode);

      // Parse code
      const codeParseResult = parseCodeDirectory(codeDir);
      const callGraphResult = extractCallGraph(codeParseResult);

      // Build knowledge graph with code integration
      const result = await buildKnowledgeGraph(specsDir, {
        dryRun: false,
        outputDir: testDir,
        codeParseResult,
        callGraphResult,
      });

      // Verify specs were processed
      expect(result.summary.specsCount).toBe(1);

      // Verify code functions were added
      expect(result.summary.functionsCount).toBeGreaterThan(0);
      expect(result.summary.workflowCallsCount).toBeGreaterThan(0);

      // Read the generated JSON-LD file
      const fs = await import('fs/promises');
      const jsonldContent = await fs.readFile(
        join(testDir, 'kg.jsonld'),
        'utf-8'
      );
      const kg = JSON.parse(jsonldContent);

      // Verify function nodes exist in the graph
      const functionNodes = (kg as JsonLdGraph)['@graph'].filter(
        (node: JsonLdNode) => node['@type'] === 'function'
      );
      expect(functionNodes.length).toBeGreaterThan(0);

      // Verify function node structure
      const calculateFunction = functionNodes.find(
        (node: JsonLdNode) => node.name === 'calculate'
      );
      expect(calculateFunction).toBeDefined();
      expect(calculateFunction!['@id']).toMatch(/^function:[a-f0-9]{8}$/);
      expect(calculateFunction!.signature).toBe('calculate(x, y)');
      expect(calculateFunction!.lang).toBe('python');
      expect(calculateFunction!.file).toContain('math.py');

      // Verify workflow_calls edges exist
      const workflowEdges = (kg as JsonLdGraph)['@graph'].filter(
        (edge: JsonLdNode) => edge['@type'] === 'workflow_calls'
      );
      expect(workflowEdges.length).toBeGreaterThan(0);

      // Verify edge structure
      const edge = workflowEdges[0]!;
      expect(edge['@id']).toMatch(/^call:[a-f0-9]{8}$/);
      expect(edge.source).toMatch(/^function:[a-f0-9]{8}$/);
      expect(edge.target).toMatch(/^function:[a-f0-9]{8}$/);
      expect(edge.call_type).toBe('direct');
      expect(edge.confidence).toBe(1.0);
    });

    it('should handle specs-only knowledge graph (no code)', async () => {
      // Create test spec file
      const specContent = `---
title: "Spec Only"
description: "Specification without code"
version: "1.0.0"
status: "Draft"
---

# Spec Only

No code integration.
`;
      writeFileSync(join(specsDir, 'spec-only.mdx'), specContent);

      // Build knowledge graph without code
      const result = await buildKnowledgeGraph(specsDir, {
        dryRun: false,
        outputDir: testDir,
      });

      expect(result.summary.specsCount).toBe(1);
      expect(result.summary.functionsCount).toBe(0);
      expect(result.summary.workflowCallsCount).toBe(0);

      // Verify no function nodes in output
      const fs = await import('fs/promises');
      const jsonldContent = await fs.readFile(
        join(testDir, 'kg.jsonld'),
        'utf-8'
      );
      const kg = JSON.parse(jsonldContent);

      const functionNodes = (kg as JsonLdGraph)['@graph'].filter(
        (node: JsonLdNode) => node['@type'] === 'function'
      );
      expect(functionNodes.length).toBe(0);
    });

    it('should handle code-only knowledge graph (no specs)', async () => {
      // Create test code files only
      const pythonCode = `
def standalone():
    return "no specs"
`;
      writeFileSync(join(codeDir, 'standalone.py'), pythonCode);

      // Parse code
      const codeParseResult = parseCodeDirectory(codeDir);
      const callGraphResult = extractCallGraph(codeParseResult);

      // Build knowledge graph with code only (empty specs dir)
      const result = await buildKnowledgeGraph(specsDir, {
        dryRun: false,
        outputDir: testDir,
        codeParseResult,
        callGraphResult,
      });

      expect(result.summary.specsCount).toBe(0);
      expect(result.summary.functionsCount).toBe(1);
      expect(result.summary.workflowCallsCount).toBe(0);

      // Verify function node exists
      const fs = await import('fs/promises');
      const jsonldContent = await fs.readFile(
        join(testDir, 'kg.jsonld'),
        'utf-8'
      );
      const kg = JSON.parse(jsonldContent);

      const functionNodes = (kg as JsonLdGraph)['@graph'].filter(
        (node: JsonLdNode) => node['@type'] === 'function'
      );
      expect(functionNodes.length).toBe(1);
      expect(functionNodes[0]!.name).toBe('standalone');
    });

    it('should maintain deterministic IDs across builds', async () => {
      // Create test code
      const pythonCode = `
def consistent():
    return "same"
`;
      writeFileSync(join(codeDir, 'consistent.py'), pythonCode);

      // Parse code
      const codeParseResult = parseCodeDirectory(codeDir);
      const callGraphResult = extractCallGraph(codeParseResult);

      // Build knowledge graph twice
      const result1 = await buildKnowledgeGraph(specsDir, {
        dryRun: false,
        outputDir: testDir,
        codeParseResult,
        callGraphResult,
      });

      expect(result1.errors).toHaveLength(0);

      const fs = await import('fs/promises');
      const jsonld1 = await fs.readFile(join(testDir, 'kg.jsonld'), 'utf-8');

      // Build again
      const result2 = await buildKnowledgeGraph(specsDir, {
        dryRun: false,
        outputDir: testDir,
        codeParseResult,
        callGraphResult,
      });

      expect(result2.errors).toHaveLength(0);

      const jsonld2 = await fs.readFile(join(testDir, 'kg.jsonld'), 'utf-8');

      // IDs should be consistent
      const kg1 = JSON.parse(jsonld1);
      const kg2 = JSON.parse(jsonld2);

      const func1 = (kg1 as JsonLdGraph)['@graph'].find(
        (node: JsonLdNode) => node['@type'] === 'function'
      );
      const func2 = (kg2 as JsonLdGraph)['@graph'].find(
        (node: JsonLdNode) => node['@type'] === 'function'
      );

      expect(func1!['@id']).toBe(func2!['@id']);
    });
  });

  describe('CLI Integration', () => {
    it('should produce knowledge graph with function nodes via CLI', async () => {
      // Create test files
      const specContent = `---
title: "CLI Test"
description: "CLI integration test"
version: "1.0.0"
status: "Draft"
---

# CLI Test
`;
      const codeContent = `
def cli_test():
    return "success"
`;

      writeFileSync(join(specsDir, 'cli-test.mdx'), specContent);
      writeFileSync(join(codeDir, 'cli-test.py'), codeContent);

      // Import and test CLI function
      const { exec } = await import('child_process');
      const { promisify } = await import('util');
      const execAsync = promisify(exec);

      // Run CLI command
      const { stdout } = await execAsync(
        `node dist/build-kg.js --code ${codeDir} ${specsDir}`,
        { cwd: join(__dirname, '..') }
      );

      expect(stdout).toContain('Functions added: 1');
      expect(stdout).toContain('Knowledge graph built successfully');

      // Verify output files contain function data
      const fs = await import('fs/promises');
      const jsonldContent = await fs.readFile(
        join(__dirname, '../kg.jsonld'),
        'utf-8'
      );
      const kg = JSON.parse(jsonldContent);

      const functionNodes = (kg as JsonLdGraph)['@graph'].filter(
        (node: JsonLdNode) => node['@type'] === 'function'
      );
      expect(functionNodes.length).toBeGreaterThan(0);
    });
  });
});
