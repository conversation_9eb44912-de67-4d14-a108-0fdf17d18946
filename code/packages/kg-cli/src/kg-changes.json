{"timestamp": "2025-06-01T20:12:12.224Z", "summary": {"nodesAdded": 14, "nodesUpdated": 13, "nodesMarkedStale": 0, "edgesAdded": 12, "edgesUpdated": 3, "edgesMarkedStale": 0}, "coverage": [], "errors": [{"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/src/sync.ts", "line": 1}, {"message": "File name \"sync.ts\" doesn't match component \"SyncOrchestrator\"", "severity": "warning", "file": "code/packages/kg-sync-lib/src/sync.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "Multiple @implements annotations found for M1.2#AnnotationParser", "severity": "warning", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "File name \"parseAnnotations.test.ts\" doesn't match component \"AnnotationParser\"", "severity": "warning", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "Duplicate @implements annotation for M1.2#AnnotationParser", "severity": "warning", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "File name \"parseAnnotations.test.ts\" doesn't match component \"AnnotationParser\"", "severity": "warning", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "File name \"parseAnnotations.test.ts\" doesn't match component \"ValidationEngine\"", "severity": "warning", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "File name \"parseAnnotations.test.ts\" doesn't match component \"ParserClass\"", "severity": "warning", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "File name \"parseAnnotations.test.ts\" doesn't match component \"ParseMethod\"", "severity": "warning", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "File name \"parseAnnotations.test.ts\" doesn't match component \"ArrowFunction\"", "severity": "warning", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "Missing \"milestone-\" prefix", "severity": "error", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "Invalid @implements format: \"M1.2#InvalidFormat\"", "severity": "error", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "Invalid @implements format: \"milestone-M1.2#123Invalid\"", "severity": "error", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "@implements tag is empty", "severity": "error", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "File name \"parseAnnotations.test.ts\" doesn't match component \"OrphanAnnotation\"", "severity": "warning", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "File name \"parseAnnotations.test.ts\" doesn't match component \"ValidAnnotation\"", "severity": "warning", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "File name \"parseAnnotations.test.ts\" doesn't match component \"OuterFunction\"", "severity": "warning", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "File name \"parseAnnotations.test.ts\" doesn't match component \"InnerFunction\"", "severity": "warning", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "File name \"parseAnnotations.test.ts\" doesn't match component \"ComplexComponent\"", "severity": "warning", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}]}