{"timestamp": "2025-06-01T21:35:13.336Z", "summary": {"nodesAdded": 14, "nodesUpdated": 2, "nodesMarkedStale": 0, "edgesAdded": 7, "edgesUpdated": 2, "edgesMarkedStale": 0}, "coverage": [{"milestoneId": "Mm0", "totalComponents": 0, "implementedComponents": 0, "coverage": 1, "confidence": 0, "lastUpdated": "2025-06-01T21:35:13.332Z"}, {"milestoneId": "<PERSON><PERSON><PERSON><PERSON>", "totalComponents": 0, "implementedComponents": 0, "coverage": 1, "confidence": 0, "lastUpdated": "2025-06-01T21:35:13.332Z"}, {"milestoneId": "Mm1", "totalComponents": 0, "implementedComponents": 0, "coverage": 1, "confidence": 0, "lastUpdated": "2025-06-01T21:35:13.332Z"}, {"milestoneId": "Mtest", "totalComponents": 0, "implementedComponents": 0, "coverage": 1, "confidence": 0, "lastUpdated": "2025-06-01T21:35:13.332Z"}, {"milestoneId": "Mimplementation", "totalComponents": 0, "implementedComponents": 0, "coverage": 1, "confidence": 0, "lastUpdated": "2025-06-01T21:35:13.332Z"}, {"milestoneId": "Mtemplate", "totalComponents": 0, "implementedComponents": 0, "coverage": 1, "confidence": 0, "lastUpdated": "2025-06-01T21:35:13.332Z"}], "errors": [{"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/src/confidence.ts", "line": 1}, {"message": "File name \"confidence.ts\" doesn't match component \"ConfidenceScoring\"", "severity": "warning", "file": "code/packages/kg-sync-lib/src/confidence.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/src/diffGit.ts", "line": 1}, {"message": "File name \"diffGit.ts\" doesn't match component \"GitDiffCore\"", "severity": "warning", "file": "code/packages/kg-sync-lib/src/diffGit.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/src/extractComponents.ts", "line": 1}, {"message": "File name \"extractComponents.ts\" doesn't match component \"ComponentExtraction\"", "severity": "warning", "file": "code/packages/kg-sync-lib/src/extractComponents.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/src/updateGraph.ts", "line": 1}, {"message": "File name \"updateGraph.ts\" doesn't match component \"GraphUpdateCore\"", "severity": "warning", "file": "code/packages/kg-sync-lib/src/updateGraph.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/src/validation.ts", "line": 1}, {"message": "File name \"validation.ts\" doesn't match component \"ValidationEngine\"", "severity": "warning", "file": "code/packages/kg-sync-lib/src/validation.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/src/parseAnnotations.ts", "line": 1}, {"message": "Multiple @implements annotations found for M1.2#AnnotationParser", "severity": "warning", "file": "code/packages/kg-sync-lib/src/parseAnnotations.ts", "line": 1}, {"message": "File name \"parseAnnotations.ts\" doesn't match component \"AnnotationParser\"", "severity": "warning", "file": "code/packages/kg-sync-lib/src/parseAnnotations.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/src/parseAnnotations.ts", "line": 1}, {"message": "Duplicate @implements annotation for M1.2#AnnotationParser", "severity": "warning", "file": "code/packages/kg-sync-lib/src/parseAnnotations.ts", "line": 1}, {"message": "File name \"parseAnnotations.ts\" doesn't match component \"AnnotationParser\"", "severity": "warning", "file": "code/packages/kg-sync-lib/src/parseAnnotations.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/src/sync.ts", "line": 1}, {"message": "Multiple @implements annotations found for M1.2#SyncOrchestrator", "severity": "warning", "file": "code/packages/kg-sync-lib/src/sync.ts", "line": 1}, {"message": "File name \"sync.ts\" doesn't match component \"SyncOrchestrator\"", "severity": "warning", "file": "code/packages/kg-sync-lib/src/sync.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/src/sync.ts", "line": 1}, {"message": "Duplicate @implements annotation for M1.2#SyncOrchestrator", "severity": "warning", "file": "code/packages/kg-sync-lib/src/sync.ts", "line": 1}, {"message": "File name \"sync.ts\" doesn't match component \"SyncOrchestrator\"", "severity": "warning", "file": "code/packages/kg-sync-lib/src/sync.ts", "line": 1}]}