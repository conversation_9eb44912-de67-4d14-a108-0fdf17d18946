{"timestamp": "2025-06-01T21:16:26.059Z", "summary": {"nodesAdded": 31, "nodesUpdated": 18, "nodesMarkedStale": 0, "edgesAdded": 23, "edgesUpdated": 7, "edgesMarkedStale": 0}, "coverage": [{"milestoneId": "Mm0", "totalComponents": 0, "implementedComponents": 0, "coverage": 1, "confidence": 0, "lastUpdated": "2025-06-01T21:16:25.991Z"}, {"milestoneId": "<PERSON><PERSON><PERSON><PERSON>", "totalComponents": 0, "implementedComponents": 0, "coverage": 1, "confidence": 0, "lastUpdated": "2025-06-01T21:16:25.991Z"}, {"milestoneId": "Mm1", "totalComponents": 0, "implementedComponents": 0, "coverage": 1, "confidence": 0, "lastUpdated": "2025-06-01T21:16:25.991Z"}, {"milestoneId": "Mtest", "totalComponents": 0, "implementedComponents": 0, "coverage": 1, "confidence": 0, "lastUpdated": "2025-06-01T21:16:25.991Z"}, {"milestoneId": "Mimplementation", "totalComponents": 0, "implementedComponents": 0, "coverage": 1, "confidence": 0, "lastUpdated": "2025-06-01T21:16:25.991Z"}, {"milestoneId": "Mtemplate", "totalComponents": 0, "implementedComponents": 0, "coverage": 1, "confidence": 0, "lastUpdated": "2025-06-01T21:16:25.991Z"}], "errors": [{"message": "File name \"acceptance-test.ts\" doesn't match component \"AcceptanceTestComponent\"", "severity": "warning", "file": "code/acceptance-test.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-cli/src/sync-kg.ts", "line": 1}, {"message": "File name \"sync-kg.ts\" doesn't match component \"CLIIntegration\"", "severity": "warning", "file": "code/packages/kg-cli/src/sync-kg.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/src/confidence.ts", "line": 1}, {"message": "File name \"confidence.ts\" doesn't match component \"ConfidenceScoring\"", "severity": "warning", "file": "code/packages/kg-sync-lib/src/confidence.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/src/diffGit.ts", "line": 1}, {"message": "File name \"diffGit.ts\" doesn't match component \"GitDiffCore\"", "severity": "warning", "file": "code/packages/kg-sync-lib/src/diffGit.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/src/extractComponents.ts", "line": 1}, {"message": "File name \"extractComponents.ts\" doesn't match component \"ComponentExtraction\"", "severity": "warning", "file": "code/packages/kg-sync-lib/src/extractComponents.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/src/parseAnnotations.ts", "line": 1}, {"message": "Multiple @implements annotations found for M1.2#AnnotationParser", "severity": "warning", "file": "code/packages/kg-sync-lib/src/parseAnnotations.ts", "line": 1}, {"message": "File name \"parseAnnotations.ts\" doesn't match component \"AnnotationParser\"", "severity": "warning", "file": "code/packages/kg-sync-lib/src/parseAnnotations.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/src/parseAnnotations.ts", "line": 1}, {"message": "Duplicate @implements annotation for M1.2#AnnotationParser", "severity": "warning", "file": "code/packages/kg-sync-lib/src/parseAnnotations.ts", "line": 1}, {"message": "File name \"parseAnnotations.ts\" doesn't match component \"AnnotationParser\"", "severity": "warning", "file": "code/packages/kg-sync-lib/src/parseAnnotations.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/src/sync.ts", "line": 1}, {"message": "Multiple @implements annotations found for M1.2#SyncOrchestrator", "severity": "warning", "file": "code/packages/kg-sync-lib/src/sync.ts", "line": 1}, {"message": "File name \"sync.ts\" doesn't match component \"SyncOrchestrator\"", "severity": "warning", "file": "code/packages/kg-sync-lib/src/sync.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/src/sync.ts", "line": 1}, {"message": "Duplicate @implements annotation for M1.2#SyncOrchestrator", "severity": "warning", "file": "code/packages/kg-sync-lib/src/sync.ts", "line": 1}, {"message": "File name \"sync.ts\" doesn't match component \"SyncOrchestrator\"", "severity": "warning", "file": "code/packages/kg-sync-lib/src/sync.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/src/updateGraph.ts", "line": 1}, {"message": "File name \"updateGraph.ts\" doesn't match component \"GraphUpdateCore\"", "severity": "warning", "file": "code/packages/kg-sync-lib/src/updateGraph.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/src/validation.ts", "line": 1}, {"message": "File name \"validation.ts\" doesn't match component \"ValidationEngine\"", "severity": "warning", "file": "code/packages/kg-sync-lib/src/validation.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "Multiple @implements annotations found for M1.2#AnnotationParser", "severity": "warning", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "File name \"parseAnnotations.test.ts\" doesn't match component \"AnnotationParser\"", "severity": "warning", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "Duplicate @implements annotation for M1.2#AnnotationParser", "severity": "warning", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "File name \"parseAnnotations.test.ts\" doesn't match component \"AnnotationParser\"", "severity": "warning", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "File name \"parseAnnotations.test.ts\" doesn't match component \"ValidationEngine\"", "severity": "warning", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "File name \"parseAnnotations.test.ts\" doesn't match component \"ParserClass\"", "severity": "warning", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "File name \"parseAnnotations.test.ts\" doesn't match component \"ParseMethod\"", "severity": "warning", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "File name \"parseAnnotations.test.ts\" doesn't match component \"ArrowFunction\"", "severity": "warning", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "Missing \"milestone-\" prefix", "severity": "error", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "Invalid @implements format: \"M1.2#InvalidFormat\"", "severity": "error", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "Invalid @implements format: \"milestone-M1.2#123Invalid\"", "severity": "error", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "@implements tag is empty", "severity": "error", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "File name \"parseAnnotations.test.ts\" doesn't match component \"OrphanAnnotation\"", "severity": "warning", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "File name \"parseAnnotations.test.ts\" doesn't match component \"ValidAnnotation\"", "severity": "warning", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "File name \"parseAnnotations.test.ts\" doesn't match component \"OuterFunction\"", "severity": "warning", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "File name \"parseAnnotations.test.ts\" doesn't match component \"InnerFunction\"", "severity": "warning", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "File name \"parseAnnotations.test.ts\" doesn't match component \"ComplexComponent\"", "severity": "warning", "file": "code/packages/kg-sync-lib/tests/parseAnnotations.test.ts", "line": 1}, {"message": "Invalid @implements format: \"milestone-M1.2#Component${i\"", "severity": "error", "file": "code/packages/kg-sync-lib/tests/performance.test.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/tests/performance.test.ts", "line": 1}, {"message": "Invalid @implements format: \"milestone-M1.2#Component${i\"", "severity": "error", "file": "code/packages/kg-sync-lib/tests/performance.test.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/tests/performance.test.ts", "line": 1}, {"message": "Invalid @implements format: \"milestone-M1.2#Component${i\"", "severity": "error", "file": "code/packages/kg-sync-lib/tests/performance.test.ts", "line": 1}, {"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-sync-lib/tests/performance.test.ts", "line": 1}, {"message": "File name \"test-annotations.ts\" doesn't match component \"AnnotationParser\"", "severity": "warning", "file": "code/test-annotations.ts", "line": 1}, {"message": "Function name \"parseAnnotations\" doesn't match component \"GitDiffDetector\"", "severity": "warning", "file": "code/test-annotations.ts", "line": 1}, {"message": "File name \"test-annotations.ts\" doesn't match component \"GitDiffDetector\"", "severity": "warning", "file": "code/test-annotations.ts", "line": 1}]}