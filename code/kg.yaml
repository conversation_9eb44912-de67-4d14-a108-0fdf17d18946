"@context":
  "@vocab": https://workflow-mapper.dev/vocab#
  title: https://schema.org/name
  description: https://schema.org/description
  status: https://workflow-mapper.dev/vocab#status
  version: https://schema.org/version
  created: https://schema.org/dateCreated
  updated: https://schema.org/dateModified
  tags: https://schema.org/keywords
  authors: https://schema.org/author
  filePath: https://workflow-mapper.dev/vocab#filePath
  implements: https://workflow-mapper.dev/vocab#implements
  dependsOn: https://workflow-mapper.dev/vocab#dependsOn
  contains: https://workflow-mapper.dev/vocab#contains
"@graph":
  - "@id": spec----docs-tech-specs-adrs-adr-001-monorepo-mdx-adr-001---monorepo-structure-with-pnpm-workspaces
    "@type": ArchitecturalDecision
    filePath: ../docs/tech-specs/adrs/adr-001-monorepo.mdx
    title: ADR-001 — Monorepo Structure with pnpm Workspaces
    description: Decision to use pnpm workspaces with apps/ and packages/ structure
      instead of separate repositories.
    created: 2025-05-25T00:00:00.000Z
    updated: 2025-05-25T00:00:00.000Z
    version: 1.0.0
    status: Accepted
    tags:
      - adr
      - architecture
      - monorepo
    authors:
      - nitishMehrotra
  - "@id": spec----docs-tech-specs-adrs-adr-002-typescript-mdx-adr-002---typescript-first-development
    "@type": ArchitecturalDecision
    filePath: ../docs/tech-specs/adrs/adr-002-typescript.mdx
    title: ADR-002 — TypeScript-First Development
    description: Decision to use strict TypeScript across frontend, backend, and
      shared code.
    created: 2025-05-25T00:00:00.000Z
    updated: 2025-05-25T00:00:00.000Z
    version: 1.0.0
    status: Accepted
    tags:
      - adr
      - architecture
      - typescript
    authors:
      - nitishMehrotra
  - "@id": spec----docs-tech-specs-adrs-adr-003-jsonld-mdx-adr-003---json-ld-for-graph-representation
    "@type": ArchitecturalDecision
    filePath: ../docs/tech-specs/adrs/adr-003-jsonld.mdx
    title: ADR-003 — JSON-LD for Graph Representation
    description: Decision to use JSON-LD as the canonical format for representing
      workflow graphs.
    created: 2025-05-25T00:00:00.000Z
    updated: 2025-05-25T00:00:00.000Z
    version: 1.0.0
    status: Accepted
    tags:
      - adr
      - architecture
      - data-format
    authors:
      - nitishMehrotra
  - "@id": spec----docs-tech-specs-adrs-adr-004-consolidated-gitignore-strateg-mdx-adr-004---consolidated--gitignore-strategy
    "@type": ArchitecturalDecision
    filePath: ../docs/tech-specs/adrs/adr-004-consolidated-gitignore-strateg.mdx
    title: ADR-004 — Consolidated .gitignore Strategy
    description: Decision to consolidate multiple .gitignore files into a single
      root-level file with comprehensive coverage for all project tools and
      technologies.
    created: 2025-05-29T00:00:00.000Z
    updated: 2025-05-29T00:00:00.000Z
    version: 1.0.0
    status: Accepted
    tags:
      - adr
      - repository-management
      - tooling
    authors:
      - nitishMehrotra
  - "@id": spec----docs-tech-specs-adrs-adr-005-knowledge-graph-system-archite-mdx-adr-005---knowledge-graph-system-architecture
    "@type": ArchitecturalDecision
    filePath: ../docs/tech-specs/adrs/adr-005-knowledge-graph-system-archite.mdx
    title: ADR-005 — Knowledge Graph System Architecture
    description: Unified architecture combining specification parsing (M0.1) and
      static code analysis (M1.1) for comprehensive knowledge graph generation.
    created: 2025-01-27T00:00:00.000Z
    updated: 2025-01-27T00:00:00.000Z
    version: 1.0.0
    status: Accepted
    tags:
      - adr
      - architecture
      - knowledge-graph
      - static-analysis
      - specifications
    authors:
      - WorkflowMapper Team
  - "@id": spec----docs-tech-specs-adrs-adr-006-bidirectional-sync-architectur-mdx-adr-006---bidirectional-sync-architecture-for-knowledge-graph
    "@type": ArchitecturalDecision
    filePath: ../docs/tech-specs/adrs/adr-006-bidirectional-sync-architectur.mdx
    title: ADR-006 — Bidirectional Sync Architecture for Knowledge Graph
    description: Architecture for bidirectional synchronization between
      specifications and code using incremental git diff and annotation parsing.
    created: 2025-06-01T00:00:00.000Z
    updated: 2025-06-01T00:00:00.000Z
    version: 1.0.0
    status: Accepted
    tags:
      - adr
      - architecture
      - bidirectional-sync
      - knowledge-graph
      - git-diff
    authors:
      - nitishMehrotra
  - "@id": spec----docs-tech-specs-adrs-adr-007-jsdoc-annotation-parsing-strat-mdx-adr-007---jsdoc-annotation-parsing-strategy
    "@type": ArchitecturalDecision
    filePath: ../docs/tech-specs/adrs/adr-007-jsdoc-annotation-parsing-strat.mdx
    title: ADR-007 — JSDoc Annotation Parsing Strategy
    description: Strategy for parsing @implements annotations from JSDoc/TSDoc
      comments to establish spec-to-code relationships.
    created: 2025-06-01T00:00:00.000Z
    updated: 2025-06-01T00:00:00.000Z
    version: 1.0.0
    status: Accepted
    tags:
      - adr
      - architecture
      - jsdoc
      - annotation-parsing
      - comment-parser
    authors:
      - nitishMehrotra
  - "@id": spec----docs-tech-specs-adrs-adr-008-git-diff-integration-for-incre-mdx-adr-008---git-diff-integration-for-incremental-updates
    "@type": ArchitecturalDecision
    filePath: ../docs/tech-specs/adrs/adr-008-git-diff-integration-for-incre.mdx
    title: ADR-008 — Git Diff Integration for Incremental Updates
    description: Strategy for using git diff to detect changed files and enable
      incremental knowledge graph updates with optimal performance.
    created: 2025-06-01T00:00:00.000Z
    updated: 2025-06-01T00:00:00.000Z
    version: 1.0.0
    status: Accepted
    tags:
      - adr
      - architecture
      - git-diff
      - incremental-updates
      - simple-git
    authors:
      - nitishMehrotra
  - "@id": spec----docs-tech-specs-adrs-log-mdx-architectural-decision-records--adrs-
    "@type": ArchitecturalDecision
    filePath: ../docs/tech-specs/adrs/log.mdx
    title: Architectural Decision Records (ADRs)
    description: Log of all significant architectural decisions made during development.
    created: 2025-05-25T00:00:00.000Z
    updated: 2025-05-25T00:00:00.000Z
    version: 0.1.0
    status: Living
    tags:
      - architecture
      - decisions
    authors:
      - nitishMehrotra
  - "@id": spec----docs-tech-specs-archived-adrs-adr-007-docusaurus-mdx-adr-007---docusaurus-for-documentation-site
    "@type": ArchitecturalDecision
    filePath: ../docs/tech-specs/archived/adrs/adr-007-docusaurus.mdx
    title: ADR-007 — Docusaurus for Documentation Site
    description: Decision to use Docusaurus 2 as the static site generator for
      rendering technical specifications and project documentation.
    created: 2025-01-25T00:00:00.000Z
    updated: 2025-01-25T00:00:00.000Z
    version: 1.0.0
    status: Accepted
    tags:
      - adr
      - architecture
      - documentation
      - docusaurus
    authors:
      - nitishMehrotra
  - "@id": spec----docs-tech-specs-archived-milestones-milestone-m0-1-content-processing-validation-mdx-content-processing-solution---validation-experiment
    "@type": Milestone
    filePath: ../docs/tech-specs/archived/milestones/milestone-m0.1/content-processing-validation.mdx
    title: Content Processing Solution - Validation Experiment
    description: Testing and validating the content processing approach before integration
    created: 2025-01-26T00:00:00.000Z
    version: 0.1.0
    status: Experimental
    tags:
      - experiment
      - validation
      - content-processing
    authors:
      - nitishMehrotra
  - "@id": spec----docs-tech-specs-archived-milestones-milestone-m0-1-milestone-m0-1-mdx--archived--milestone-m0-1---docusaurus-documentation-site
    "@type": Milestone
    filePath: ../docs/tech-specs/archived/milestones/milestone-m0.1/milestone-M0.1.mdx
    title: "[ARCHIVED] Milestone M0.1 — Docusaurus Documentation Site"
    description: "[ARCHIVED] Original attempt at documentation site - superseded by
      WorkflowMapperAgent approach"
    created: 2025-01-25T00:00:00.000Z
    archived: 2025-01-26T00:00:00.000Z
    version: 0.1.0
    status: Archived
    tags:
      - milestone
      - documentation
      - docusaurus
      - archived
    authors:
      - nitishMehrotra
    archive_reason: Pivoted to WorkflowMapperAgent foundation after discovering
      alignment with broader vision
    superseded_by: milestone-experiment-1.md and upcoming ChatGPT milestone revision
  - "@id": spec----docs-tech-specs-archived-milestones-milestone-m0-1-milestone-pivot-decision-mdx-milestone-pivot-decision---from-documentation-to-workflowmapperagent
    "@type": Milestone
    filePath: ../docs/tech-specs/archived/milestones/milestone-m0.1/milestone-pivot-decision.mdx
    title: Milestone Pivot Decision - From Documentation to WorkflowMapperAgent
    description: Decision record for pivoting from M0.1 Docusaurus approach to
      WorkflowMapperAgent foundation
    created: 2025-01-26T00:00:00.000Z
    version: 1.0.0
    status: Approved
    tags:
      - decision
      - pivot
      - architecture
      - workflow-mapper
    authors:
      - nitishMehrotra
  - "@id": spec----docs-tech-specs-dependencies-mdx-dependencies
    "@type": Specification
    filePath: ../docs/tech-specs/dependencies.mdx
  - "@id": spec----docs-tech-specs-domains-code-parser-mdx-code-parser-domain
    "@type": Domain
    filePath: ../docs/tech-specs/domains/code-parser.mdx
    title: Code Parser Domain
    description: Static code analysis and knowledge graph generation from source files
    version: 1.0.0
    status: Active
    created: 2025-01-27
    updated: 2025-01-27
    authors:
      - WorkflowMapper Team
    tags:
      - code-analysis
      - tree-sitter
      - knowledge-graph
      - static-analysis
  - "@id": spec----docs-tech-specs-domains-kg-sync-mdx-knowledge-graph-sync-domain
    "@type": Domain
    filePath: ../docs/tech-specs/domains/kg-sync.mdx
    title: Knowledge Graph Sync Domain
    description: Bidirectional synchronization between code annotations and
      milestone specifications
    version: 1.2.0
    status: Active
    created: 2025-06-01
    updated: 2025-06-01
    authors:
      - WorkflowMapper Team
    tags:
      - bidirectional-sync
      - annotations
      - git-diff
      - knowledge-graph
      - incremental-updates
  - "@id": spec----docs-tech-specs-guides-agent-configuration-guide-mdx-agent-configuration-guide
    "@type": Specification
    filePath: ../docs/tech-specs/guides/agent-configuration-guide.mdx
    title: Agent Configuration Guide
    description: How to configure AI agents with milestone process requirements
    created: 2025-05-25
    updated: 2025-05-25
    version: 1.0.0
    status: Active
    tags:
      - process
      - agents
      - configuration
      - quality
    authors:
      - nitishMehrotra
  - "@id": spec----docs-tech-specs-milestones-log-mdx-milestone-progress-log
    "@type": Milestone
    filePath: ../docs/tech-specs/milestones/log.mdx
    title: Milestone Progress Log
    description: Index and progress tracking for all project milestones.
    created: 2025-05-25T00:00:00.000Z
    updated: 2025-05-25T00:00:00.000Z
    version: 0.1.0
    status: Living
    tags:
      - milestones
      - progress
    authors:
      - nitishMehrotra
  - "@id": spec----docs-tech-specs-milestones-milestone-m0-1-mdx-milestone-m0-1---knowledge-graph-bootstrap
    "@type": Milestone
    filePath: ../docs/tech-specs/milestones/milestone-M0.1.mdx
    title: Milestone M0.1 — Knowledge-Graph Bootstrap
    description: Parse existing MDX specs into a JSON-LD + YAML graph; emit CLI
      tools every agent can run.
    created: 2025-01-25T00:00:00.000Z
    version: 0.2.0
    status: Draft
    tags:
      - milestone
    authors:
      - WorkflowMapper Team
  - "@id": spec----docs-tech-specs-milestones-milestone-m0-mdx-milestone-m0---repository-skeleton---ci
    "@type": Milestone
    filePath: ../docs/tech-specs/milestones/milestone-M0.mdx
    title: Milestone M0 — Repository Skeleton & CI
    description: The contractual scope, decisions, and acceptance tests for the very
      first deliverable.
    created: 2025-05-25T00:00:00.000Z
    updated: 2025-05-25T00:00:00.000Z
    version: 0.5.0
    status: Completed
    tags:
      - milestone
    authors:
      - nitishMehrotra
  - "@id": spec----docs-tech-specs-milestones-milestone-m1-1-mdx-milestone-m1---static-code-parser---graph-augmenter
    "@type": Milestone
    filePath: ../docs/tech-specs/milestones/milestone-M1.1.mdx
    title: Milestone M1 — Static Code Parser & Graph Augmenter
    description: Parse Python, JavaScript, and TypeScript source files with
      Tree-sitter, extract functions & call-graph, and merge results into the
      existing KG (kg.jsonld / kg.yaml).
    created: 2025-05-29T00:00:00.000Z
    version: 0.2.0
    status: Complete
    tags:
      - milestone
    authors:
      - WorkflowMapper Team
  - "@id": spec----docs-tech-specs-milestones-milestone-m1-2-mdx-milestone-m1-2---bidirectional-sync---incremental-diff
    "@type": Milestone
    filePath: ../docs/tech-specs/milestones/milestone-M1.2.mdx
    title: Milestone M1.2 — Bidirectional Sync & Incremental Diff
    description: Link code ↔ specs via annotations, update the KG on every git diff,
      and emit confidence / coverage metrics.
    created: 2025-05-29T00:00:00.000Z
    updated: 2025-06-01T00:00:00.000Z
    version: 0.3.3
    status: Draft
    tags:
      - milestone
    authors:
      - WorkflowMapper Team
  - "@id": spec----docs-tech-specs-milestones-milestone-test-mdx-milestone-test---agent-configuration-validation
    "@type": Milestone
    filePath: ../docs/tech-specs/milestones/milestone-TEST.mdx
    title: Milestone TEST - Agent Configuration Validation
    description: Test milestone to validate streamlined agent configuration system
    created: 2025-05-25
    updated: 2025-05-25
    version: 1.0.0
    status: Test
    tags:
      - test
      - validation
      - agent-configuration
    authors:
      - nitishMehrotra
  - "@id": spec----docs-tech-specs-process-agent-rules-augment-mdx-augment-agent-configuration
    "@type": Specification
    filePath: ../docs/tech-specs/process/agent-rules/augment.mdx
    title: Augment Agent Configuration
    description: Augment Agent specific configuration for milestone implementation
    created: 2025-05-25
    updated: 2025-05-25
    version: 1.0.0
    status: Active
    tags:
      - agent-rules
      - augment
      - executable
    authors:
      - nitishMehrotra
  - "@id": spec----docs-tech-specs-process-agent-rules-claude-mdx-claude-anthropic-agent-configuration
    "@type": Specification
    filePath: ../docs/tech-specs/process/agent-rules/claude.mdx
    title: Claude/Anthropic Agent Configuration
    description: Claude/Anthropic specific configuration for milestone implementation
    created: 2025-05-25
    updated: 2025-05-25
    version: 1.0.0
    status: Active
    tags:
      - agent-rules
      - claude
      - anthropic
      - executable
    authors:
      - nitishMehrotra
  - "@id": spec----docs-tech-specs-process-agent-rules-copilot-mdx-github-copilot-agent-configuration
    "@type": Specification
    filePath: ../docs/tech-specs/process/agent-rules/copilot.mdx
    title: GitHub Copilot Agent Configuration
    description: GitHub Copilot specific configuration for milestone implementation
    created: 2025-05-25
    updated: 2025-05-25
    version: 1.0.0
    status: Active
    tags:
      - agent-rules
      - copilot
      - github
      - executable
    authors:
      - nitishMehrotra
  - "@id": spec----docs-tech-specs-process-agent-rules-core-mdx-core-agent-rules
    "@type": Specification
    filePath: ../docs/tech-specs/process/agent-rules/core.mdx
    title: Core Agent Rules
    description: Universal executable rules for all AI software engineering agents
    created: 2025-05-25
    updated: 2025-05-25
    version: 1.0.0
    status: Active
    tags:
      - agent-rules
      - core
      - executable
    authors:
      - nitishMehrotra
  - "@id": spec----docs-tech-specs-process-agent-rules-cursor-mdx-cursor-agent-configuration
    "@type": Specification
    filePath: ../docs/tech-specs/process/agent-rules/cursor.mdx
    title: Cursor Agent Configuration
    description: Cursor AI specific configuration for milestone implementation
    created: 2025-05-25
    updated: 2025-05-25
    version: 1.0.0
    status: Active
    tags:
      - agent-rules
      - cursor
      - executable
    authors:
      - nitishMehrotra
  - "@id": spec----docs-tech-specs-process-agent-rules-custom-mdx-custom-agent-configuration-template
    "@type": Specification
    filePath: ../docs/tech-specs/process/agent-rules/custom.mdx
    title: Custom Agent Configuration Template
    description: Template for configuring custom AI agents for milestone implementation
    created: 2025-05-25
    updated: 2025-05-25
    version: 1.0.0
    status: Active
    tags:
      - agent-rules
      - custom
      - template
      - executable
    authors:
      - nitishMehrotra
  - "@id": spec----docs-tech-specs-process-agent-rules-validation-mdx-agent-configuration-validation
    "@type": Specification
    filePath: ../docs/tech-specs/process/agent-rules/validation.mdx
    title: Agent Configuration Validation
    description: Tools and procedures for validating AI agent configurations
    created: 2025-05-25
    updated: 2025-05-25
    version: 1.0.0
    status: Active
    tags:
      - agent-rules
      - validation
      - testing
    authors:
      - nitishMehrotra
  - "@id": spec----docs-tech-specs-process-core-architectural-decisions-mdx-architectural-decision-process
    "@type": ArchitecturalDecision
    filePath: ../docs/tech-specs/process/core/architectural-decisions.mdx
    title: Architectural Decision Process
    description: Process for creating, reviewing, and managing Architectural
      Decision Records (ADRs)
    created: 2025-05-25
    updated: 2025-05-25
    version: 1.0.0
    status: Active
    tags:
      - process
      - architecture
      - decisions
      - adr
    authors:
      - nitishMehrotra
    owner:
      - architecture-team
  - "@id": spec----docs-tech-specs-process-core-documentation-mdx-documentation-process
    "@type": Specification
    filePath: ../docs/tech-specs/process/core/documentation.mdx
    title: Documentation Process
    description: Documentation standards, validation requirements, and maintenance
      procedures
    created: 2025-05-25
    updated: 2025-05-25
    version: 1.0.0
    status: Active
    tags:
      - process
      - documentation
      - validation
      - standards
    authors:
      - nitishMehrotra
    owner:
      - documentation-team
  - "@id": spec----docs-tech-specs-process-core-error-recovery-mdx-error-recovery---rollback-process
    "@type": Specification
    filePath: ../docs/tech-specs/process/core/error-recovery.mdx
    title: Error Recovery & Rollback Process
    description: Procedures for handling implementation failures, errors, and
      rollback scenarios
    created: 2025-05-25
    updated: 2025-05-25
    version: 1.0.0
    status: Active
    tags:
      - process
      - error-recovery
      - rollback
      - incident-response
    authors:
      - nitishMehrotra
    owner:
      - devops-team
      - sre-team
  - "@id": spec----docs-tech-specs-process-core-git-workflow-mdx-git-workflow-process
    "@type": Specification
    filePath: ../docs/tech-specs/process/core/git-workflow.mdx
    title: Git Workflow Process
    description: Branching strategies, commit standards, and release processes
    created: 2025-05-25
    updated: 2025-05-25
    version: 1.0.0
    status: Active
    tags:
      - process
      - git
      - workflow
      - branching
    authors:
      - nitishMehrotra
    owner:
      - development-team
  - "@id": spec----docs-tech-specs-process-core-milestone-implementation-mdx-milestone-implementation-process
    "@type": Milestone
    filePath: ../docs/tech-specs/process/core/milestone-implementation.mdx
    title: Milestone Implementation Process
    description: Comprehensive process for executing milestone specifications
    created: 2025-05-25
    updated: 2025-05-25
    version: 1.0.0
    status: Active
    tags:
      - process
      - milestone
      - implementation
    authors:
      - nitishMehrotra
    owner:
      - development-team
  - "@id": spec----docs-tech-specs-process-core-quality-assurance-mdx-quality-assurance-process
    "@type": Specification
    filePath: ../docs/tech-specs/process/core/quality-assurance.mdx
    title: Quality Assurance Process
    description: Validation, testing, and review processes for ensuring high-quality
      deliverables
    created: 2025-05-25
    updated: 2025-05-25
    version: 1.0.0
    status: Active
    tags:
      - process
      - quality
      - testing
      - validation
    authors:
      - nitishMehrotra
    owner:
      - quality-assurance-team
  - "@id": spec----docs-tech-specs-process-current-status-mdx-current-project-status
    "@type": Specification
    filePath: ../docs/tech-specs/process/current-status.mdx
    title: Current Project Status
    description: Current state of the WorkflowMapperAgent development and next steps
    created: 2025-01-26T00:00:00.000Z
    version: 1.0.0
    status: Active
    tags:
      - status
      - roadmap
      - current-state
    authors:
      - nitishMehrotra
  - "@id": spec----docs-tech-specs-process-migration-guide-mdx-agent-configuration-migration-guide
    "@type": Specification
    filePath: ../docs/tech-specs/process/migration-guide.mdx
    title: Agent Configuration Migration Guide
    description: Guide for migrating to the streamlined agent configuration system
    created: 2025-05-25
    updated: 2025-05-25
    version: 1.0.0
    status: Active
    tags:
      - migration
      - agent-configuration
      - guide
    authors:
      - nitishMehrotra
  - "@id": spec----docs-tech-specs-process-optimisation-results-optimisation-result-001-results-mdx-agent-configuration-validation-results
    "@type": Specification
    filePath: ../docs/tech-specs/process/optimisation-results/optimisation-result-001-results.mdx
    title: Agent Configuration Validation Results
    description: Comprehensive validation results of streamlined agent configuration system
    created: 2025-05-25
    updated: 2025-05-25
    version: 1.0.0
    status: Completed
    tags:
      - validation
      - agent-configuration
      - optimization
      - results
    authors:
      - nitishMehrotra
  - "@id": spec----docs-tech-specs-process-templates-adr-template-mdx-adr-xxx----decision-title-
    "@type": ArchitecturalDecision
    filePath: ../docs/tech-specs/process/templates/adr-template.mdx
    title: ADR-XXX — <Decision Title>
    description: <Brief description of the architectural decision>
    created: <YYYY-MM-DD>
    updated: <YYYY-MM-DD>
    version: 0.1.0
    status: Proposed
    tags:
      - adr
      - architecture
    authors:
      - <author>
  - "@id": spec----docs-tech-specs-process-templates-domain-template-mdx-domain-spec----domain-name-
    "@type": Domain
    filePath: ../docs/tech-specs/process/templates/domain-template.mdx
    title: Domain Spec — <Domain Name>
    description: <Brief description of the domain and its scope>
    created: <YYYY-MM-DD>
    updated: <YYYY-MM-DD>
    version: 0.0.0
    status: Draft
    tags:
      - domain
      - <domain-tag>
    authors: []
  - "@id": spec----docs-tech-specs-process-templates-milestone-template-mdx-milestone--id-----one-line-scope-
    "@type": Milestone
    filePath: ../docs/tech-specs/process/templates/milestone-template.mdx
    title: Milestone <ID> — <One-line scope>
    description: <Short paragraph of intent>
    created: <YYYY-MM-DD>
    version: 0.0.0
    status: Draft
    tags:
      - milestone
    authors: []
  - "@id": spec----docs-tech-specs-process-templates-process-improvement-mdx-process-improvement-template
    "@type": Specification
    filePath: ../docs/tech-specs/process/templates/process-improvement.mdx
    title: Process Improvement Template
    description: Streamlined template for capturing process improvements and lessons learned
    created: 2025-05-25
    updated: 2025-05-25
    version: 1.0.0
    status: Active
    tags:
      - template
      - process-improvement
      - lessons-learned
      - agent-ready
    authors:
      - nitishMehrotra
  - "@id": spec----docs-tech-specs-process-templates-requirement-checklist-mdx-requirement-checklist-template
    "@type": Specification
    filePath: ../docs/tech-specs/process/templates/requirement-checklist.mdx
    title: Requirement Checklist Template
    description: Streamlined pre-implementation validation checklist
    created: 2025-05-25
    updated: 2025-05-25
    version: 1.0.0
    status: Active
    tags:
      - template
      - checklist
      - requirements
      - agent-ready
    authors:
      - nitishMehrotra
  - "@id": spec----docs-tech-specs-process-templates-work-log-template-mdx-work-log-template
    "@type": Milestone
    filePath: ../docs/tech-specs/process/templates/work-log-template.mdx
    title: Work Log Template
    description: Streamlined template for milestone implementation work logs
    created: 2025-05-25
    updated: 2025-05-25
    version: 1.0.0
    status: Active
    tags:
      - template
      - work-log
      - milestone
      - agent-ready
    authors:
      - nitishMehrotra
  - "@id": spec----docs-tech-specs-spec-checklist-mdx-spec-checklist
    "@type": Specification
    filePath: ../docs/tech-specs/spec_checklist.mdx
    title: Spec Checklist
    description: Quick reference for milestone specification validation requirements
    version: 2.1.0
    status: Living
    tags:
      - checklist
      - validation
      - reference
  - "@id": spec----docs-tech-specs-structure-mdx-repository-structure---conventions
    "@type": Specification
    filePath: ../docs/tech-specs/structure.mdx
    title: Repository Structure & Conventions
    description: Living guideline—update with every structural PR. Single source of
      truth for project structure.
    created: 2025-05-25T00:00:00.000Z
    updated: 2025-05-25T00:00:00.000Z
    version: 0.2.0
    status: Living
    tags:
      - structure
    authors:
      - nitishMehrotra
  - "@id": function:cb5cdbdf
    "@type": function
    title: anonymous
    description: Function  in packages/code-parser-lib/coverage/block-navigation.js
    filePath: packages/code-parser-lib/coverage/block-navigation.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 86
  - "@id": function:cf760c3b
    "@type": function
    title: toggleClass
    description: Function toggleClass(index) in
      packages/code-parser-lib/coverage/block-navigation.js
    filePath: packages/code-parser-lib/coverage/block-navigation.js
    name: toggleClass
    signature: toggleClass(index)
    lang: javascript
    line_start: 24
    line_end: 29
  - "@id": function:4434cce9
    "@type": function
    title: makeCurrent
    description: Function makeCurrent(index) in
      packages/code-parser-lib/coverage/block-navigation.js
    filePath: packages/code-parser-lib/coverage/block-navigation.js
    name: makeCurrent
    signature: makeCurrent(index)
    lang: javascript
    line_start: 31
    line_end: 39
  - "@id": function:e81de177
    "@type": function
    title: goToPrevious
    description: Function goToPrevious() in
      packages/code-parser-lib/coverage/block-navigation.js
    filePath: packages/code-parser-lib/coverage/block-navigation.js
    name: goToPrevious
    signature: goToPrevious()
    lang: javascript
    line_start: 41
    line_end: 50
  - "@id": function:78c9d6c0
    "@type": function
    title: goToNext
    description: Function goToNext() in
      packages/code-parser-lib/coverage/block-navigation.js
    filePath: packages/code-parser-lib/coverage/block-navigation.js
    name: goToNext
    signature: goToNext()
    lang: javascript
    line_start: 52
    line_end: 63
  - "@id": function:aa3baab4
    "@type": function
    title: anonymous
    description: Function  in packages/code-parser-lib/coverage/block-navigation.js
    filePath: packages/code-parser-lib/coverage/block-navigation.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 65
    line_end: 85
  - "@id": function:3da3400c
    "@type": function
    title: anonymous
    description: Function  in
      packages/code-parser-lib/coverage/lcov-report/block-navigation.js
    filePath: packages/code-parser-lib/coverage/lcov-report/block-navigation.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 86
  - "@id": function:ef6be38d
    "@type": function
    title: toggleClass
    description: Function toggleClass(index) in
      packages/code-parser-lib/coverage/lcov-report/block-navigation.js
    filePath: packages/code-parser-lib/coverage/lcov-report/block-navigation.js
    name: toggleClass
    signature: toggleClass(index)
    lang: javascript
    line_start: 24
    line_end: 29
  - "@id": function:a9e66fb0
    "@type": function
    title: makeCurrent
    description: Function makeCurrent(index) in
      packages/code-parser-lib/coverage/lcov-report/block-navigation.js
    filePath: packages/code-parser-lib/coverage/lcov-report/block-navigation.js
    name: makeCurrent
    signature: makeCurrent(index)
    lang: javascript
    line_start: 31
    line_end: 39
  - "@id": function:f1b06ded
    "@type": function
    title: goToPrevious
    description: Function goToPrevious() in
      packages/code-parser-lib/coverage/lcov-report/block-navigation.js
    filePath: packages/code-parser-lib/coverage/lcov-report/block-navigation.js
    name: goToPrevious
    signature: goToPrevious()
    lang: javascript
    line_start: 41
    line_end: 50
  - "@id": function:536ea490
    "@type": function
    title: goToNext
    description: Function goToNext() in
      packages/code-parser-lib/coverage/lcov-report/block-navigation.js
    filePath: packages/code-parser-lib/coverage/lcov-report/block-navigation.js
    name: goToNext
    signature: goToNext()
    lang: javascript
    line_start: 52
    line_end: 63
  - "@id": function:6a56cb26
    "@type": function
    title: anonymous
    description: Function  in
      packages/code-parser-lib/coverage/lcov-report/block-navigation.js
    filePath: packages/code-parser-lib/coverage/lcov-report/block-navigation.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 65
    line_end: 85
  - "@id": function:06a2db0a
    "@type": function
    title: anonymous
    description: Function  in packages/code-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/code-parser-lib/coverage/lcov-report/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:dd0c7003
    "@type": function
    title: k
    description: Function k(Z) in packages/code-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/code-parser-lib/coverage/lcov-report/prettify.js
    name: k
    signature: k(Z)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:34306a9a
    "@type": function
    title: ab
    description: Function ab(ah) in
      packages/code-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/code-parser-lib/coverage/lcov-report/prettify.js
    name: ab
    signature: ab(ah)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:da14a863
    "@type": function
    title: T
    description: Function T(af) in packages/code-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/code-parser-lib/coverage/lcov-report/prettify.js
    name: T
    signature: T(af)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:a2e0d725
    "@type": function
    title: X
    description: Function X(am) in packages/code-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/code-parser-lib/coverage/lcov-report/prettify.js
    name: X
    signature: X(am)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:06a2db0a
    "@type": function
    title: anonymous
    description: Function  in packages/code-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/code-parser-lib/coverage/lcov-report/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:a6c52439
    "@type": function
    title: W
    description: Function W(al) in packages/code-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/code-parser-lib/coverage/lcov-report/prettify.js
    name: W
    signature: W(al)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:06a2db0a
    "@type": function
    title: anonymous
    description: Function  in packages/code-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/code-parser-lib/coverage/lcov-report/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:43a35ed7
    "@type": function
    title: a
    description: Function a(V) in packages/code-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/code-parser-lib/coverage/lcov-report/prettify.js
    name: a
    signature: a(V)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:eea6ec4a
    "@type": function
    title: aa
    description: Function aa(ab) in
      packages/code-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/code-parser-lib/coverage/lcov-report/prettify.js
    name: aa
    signature: aa(ab)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:77bbcbed
    "@type": function
    title: B
    description: Function B(S,U,W,T) in
      packages/code-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/code-parser-lib/coverage/lcov-report/prettify.js
    name: B
    signature: B(S,U,W,T)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:48824308
    "@type": function
    title: o
    description: Function o(S) in packages/code-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/code-parser-lib/coverage/lcov-report/prettify.js
    name: o
    signature: o(S)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:0fa1db7c
    "@type": function
    title: g
    description: Function g(U,T) in
      packages/code-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/code-parser-lib/coverage/lcov-report/prettify.js
    name: g
    signature: g(U,T)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:06a2db0a
    "@type": function
    title: anonymous
    description: Function  in packages/code-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/code-parser-lib/coverage/lcov-report/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:06a2db0a
    "@type": function
    title: anonymous
    description: Function  in packages/code-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/code-parser-lib/coverage/lcov-report/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:022cc74f
    "@type": function
    title: i
    description: Function i(T) in packages/code-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/code-parser-lib/coverage/lcov-report/prettify.js
    name: i
    signature: i(T)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:cbfdd0ea
    "@type": function
    title: Q
    description: Function Q(V,ag) in
      packages/code-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/code-parser-lib/coverage/lcov-report/prettify.js
    name: Q
    signature: Q(V,ag)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:a80d8f35
    "@type": function
    title: ae
    description: Function ae(al) in
      packages/code-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/code-parser-lib/coverage/lcov-report/prettify.js
    name: ae
    signature: ae(al)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:c53947e5
    "@type": function
    title: ad
    description: Function ad(ak) in
      packages/code-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/code-parser-lib/coverage/lcov-report/prettify.js
    name: ad
    signature: ad(ak)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:5147793a
    "@type": function
    title: ai
    description: Function ai(al,ar) in
      packages/code-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/code-parser-lib/coverage/lcov-report/prettify.js
    name: ai
    signature: ai(al,ar)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:c8a18a28
    "@type": function
    title: D
    description: Function D(ac) in packages/code-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/code-parser-lib/coverage/lcov-report/prettify.js
    name: D
    signature: D(ac)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:217434f9
    "@type": function
    title: c
    description: Function c(U,V) in
      packages/code-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/code-parser-lib/coverage/lcov-report/prettify.js
    name: c
    signature: c(U,V)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:f70ad850
    "@type": function
    title: q
    description: Function q(T,S) in
      packages/code-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/code-parser-lib/coverage/lcov-report/prettify.js
    name: q
    signature: q(T,S)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:8543d000
    "@type": function
    title: d
    description: Function d(V) in packages/code-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/code-parser-lib/coverage/lcov-report/prettify.js
    name: d
    signature: d(V)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:ac5d0357
    "@type": function
    title: y
    description: Function y(W,V,U) in
      packages/code-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/code-parser-lib/coverage/lcov-report/prettify.js
    name: y
    signature: y(W,V,U)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:30992cb3
    "@type": function
    title: b
    description: Function b(ad) in packages/code-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/code-parser-lib/coverage/lcov-report/prettify.js
    name: b
    signature: b(ad)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:c688bbd7
    "@type": function
    title: Y
    description: Function Y(af) in packages/code-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/code-parser-lib/coverage/lcov-report/prettify.js
    name: Y
    signature: Y(af)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:06a2db0a
    "@type": function
    title: anonymous
    description: Function  in packages/code-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/code-parser-lib/coverage/lcov-report/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:7f0bf11c
    "@type": function
    title: U
    description: Function U() in packages/code-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/code-parser-lib/coverage/lcov-report/prettify.js
    name: U
    signature: U()
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:14bab3df
    "@type": function
    title: anonymous
    description: Function  in packages/code-parser-lib/coverage/lcov-report/sorter.js
    filePath: packages/code-parser-lib/coverage/lcov-report/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 194
  - "@id": function:1f2b82a2
    "@type": function
    title: getTable
    description: Function getTable() in
      packages/code-parser-lib/coverage/lcov-report/sorter.js
    filePath: packages/code-parser-lib/coverage/lcov-report/sorter.js
    name: getTable
    signature: getTable()
    lang: javascript
    line_start: 11
    line_end: 13
  - "@id": function:ed0db8ce
    "@type": function
    title: getTableHeader
    description: Function getTableHeader() in
      packages/code-parser-lib/coverage/lcov-report/sorter.js
    filePath: packages/code-parser-lib/coverage/lcov-report/sorter.js
    name: getTableHeader
    signature: getTableHeader()
    lang: javascript
    line_start: 15
    line_end: 17
  - "@id": function:04340385
    "@type": function
    title: getTableBody
    description: Function getTableBody() in
      packages/code-parser-lib/coverage/lcov-report/sorter.js
    filePath: packages/code-parser-lib/coverage/lcov-report/sorter.js
    name: getTableBody
    signature: getTableBody()
    lang: javascript
    line_start: 19
    line_end: 21
  - "@id": function:09feb2d4
    "@type": function
    title: getNthColumn
    description: Function getNthColumn(n) in
      packages/code-parser-lib/coverage/lcov-report/sorter.js
    filePath: packages/code-parser-lib/coverage/lcov-report/sorter.js
    name: getNthColumn
    signature: getNthColumn(n)
    lang: javascript
    line_start: 23
    line_end: 25
  - "@id": function:44bae6b3
    "@type": function
    title: onFilterInput
    description: Function onFilterInput() in
      packages/code-parser-lib/coverage/lcov-report/sorter.js
    filePath: packages/code-parser-lib/coverage/lcov-report/sorter.js
    name: onFilterInput
    signature: onFilterInput()
    lang: javascript
    line_start: 27
    line_end: 42
  - "@id": function:2ca6fca7
    "@type": function
    title: addSearchBox
    description: Function addSearchBox() in
      packages/code-parser-lib/coverage/lcov-report/sorter.js
    filePath: packages/code-parser-lib/coverage/lcov-report/sorter.js
    name: addSearchBox
    signature: addSearchBox()
    lang: javascript
    line_start: 45
    line_end: 50
  - "@id": function:595ab877
    "@type": function
    title: loadColumns
    description: Function loadColumns() in
      packages/code-parser-lib/coverage/lcov-report/sorter.js
    filePath: packages/code-parser-lib/coverage/lcov-report/sorter.js
    name: loadColumns
    signature: loadColumns()
    lang: javascript
    line_start: 53
    line_end: 75
  - "@id": function:6bf8cf59
    "@type": function
    title: loadRowData
    description: Function loadRowData(tableRow) in
      packages/code-parser-lib/coverage/lcov-report/sorter.js
    filePath: packages/code-parser-lib/coverage/lcov-report/sorter.js
    name: loadRowData
    signature: loadRowData(tableRow)
    lang: javascript
    line_start: 78
    line_end: 95
  - "@id": function:96298e85
    "@type": function
    title: loadData
    description: Function loadData() in
      packages/code-parser-lib/coverage/lcov-report/sorter.js
    filePath: packages/code-parser-lib/coverage/lcov-report/sorter.js
    name: loadData
    signature: loadData()
    lang: javascript
    line_start: 97
    line_end: 104
  - "@id": function:b7625662
    "@type": function
    title: sortByIndex
    description: Function sortByIndex(index, desc) in
      packages/code-parser-lib/coverage/lcov-report/sorter.js
    filePath: packages/code-parser-lib/coverage/lcov-report/sorter.js
    name: sortByIndex
    signature: sortByIndex(index, desc)
    lang: javascript
    line_start: 106
    line_end: 135
  - "@id": function:99705059
    "@type": function
    title: anonymous
    description: Function  in packages/code-parser-lib/coverage/lcov-report/sorter.js
    filePath: packages/code-parser-lib/coverage/lcov-report/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 108
    line_end: 112
  - "@id": function:1d5832fc
    "@type": function
    title: anonymous
    description: Function  in packages/code-parser-lib/coverage/lcov-report/sorter.js
    filePath: packages/code-parser-lib/coverage/lcov-report/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 120
    line_end: 122
  - "@id": function:5a167e80
    "@type": function
    title: removeSortIndicators
    description: Function removeSortIndicators() in
      packages/code-parser-lib/coverage/lcov-report/sorter.js
    filePath: packages/code-parser-lib/coverage/lcov-report/sorter.js
    name: removeSortIndicators
    signature: removeSortIndicators()
    lang: javascript
    line_start: 137
    line_end: 143
  - "@id": function:33bb6ceb
    "@type": function
    title: addSortIndicators
    description: Function addSortIndicators() in
      packages/code-parser-lib/coverage/lcov-report/sorter.js
    filePath: packages/code-parser-lib/coverage/lcov-report/sorter.js
    name: addSortIndicators
    signature: addSortIndicators()
    lang: javascript
    line_start: 145
    line_end: 149
  - "@id": function:598a0b2e
    "@type": function
    title: enableUI
    description: Function enableUI() in
      packages/code-parser-lib/coverage/lcov-report/sorter.js
    filePath: packages/code-parser-lib/coverage/lcov-report/sorter.js
    name: enableUI
    signature: enableUI()
    lang: javascript
    line_start: 151
    line_end: 182
  - "@id": function:2d39f4b6
    "@type": function
    title: anonymous
    description: Function  in packages/code-parser-lib/coverage/lcov-report/sorter.js
    filePath: packages/code-parser-lib/coverage/lcov-report/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 154
    line_end: 169
  - "@id": function:0eae286b
    "@type": function
    title: anonymous
    description: Function  in packages/code-parser-lib/coverage/lcov-report/sorter.js
    filePath: packages/code-parser-lib/coverage/lcov-report/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 157
    line_end: 168
  - "@id": function:ece11d90
    "@type": function
    title: anonymous
    description: Function  in packages/code-parser-lib/coverage/lcov-report/sorter.js
    filePath: packages/code-parser-lib/coverage/lcov-report/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 184
    line_end: 193
  - "@id": function:e524ba15
    "@type": function
    title: anonymous
    description: Function  in packages/code-parser-lib/coverage/prettify.js
    filePath: packages/code-parser-lib/coverage/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:9863e291
    "@type": function
    title: k
    description: Function k(Z) in packages/code-parser-lib/coverage/prettify.js
    filePath: packages/code-parser-lib/coverage/prettify.js
    name: k
    signature: k(Z)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:6d96f7db
    "@type": function
    title: ab
    description: Function ab(ah) in packages/code-parser-lib/coverage/prettify.js
    filePath: packages/code-parser-lib/coverage/prettify.js
    name: ab
    signature: ab(ah)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:aacb295a
    "@type": function
    title: T
    description: Function T(af) in packages/code-parser-lib/coverage/prettify.js
    filePath: packages/code-parser-lib/coverage/prettify.js
    name: T
    signature: T(af)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:c22cc098
    "@type": function
    title: X
    description: Function X(am) in packages/code-parser-lib/coverage/prettify.js
    filePath: packages/code-parser-lib/coverage/prettify.js
    name: X
    signature: X(am)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:e524ba15
    "@type": function
    title: anonymous
    description: Function  in packages/code-parser-lib/coverage/prettify.js
    filePath: packages/code-parser-lib/coverage/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:85c472fb
    "@type": function
    title: W
    description: Function W(al) in packages/code-parser-lib/coverage/prettify.js
    filePath: packages/code-parser-lib/coverage/prettify.js
    name: W
    signature: W(al)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:e524ba15
    "@type": function
    title: anonymous
    description: Function  in packages/code-parser-lib/coverage/prettify.js
    filePath: packages/code-parser-lib/coverage/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:2badf74e
    "@type": function
    title: a
    description: Function a(V) in packages/code-parser-lib/coverage/prettify.js
    filePath: packages/code-parser-lib/coverage/prettify.js
    name: a
    signature: a(V)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:06b964cb
    "@type": function
    title: aa
    description: Function aa(ab) in packages/code-parser-lib/coverage/prettify.js
    filePath: packages/code-parser-lib/coverage/prettify.js
    name: aa
    signature: aa(ab)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:2c8a457e
    "@type": function
    title: B
    description: Function B(S,U,W,T) in packages/code-parser-lib/coverage/prettify.js
    filePath: packages/code-parser-lib/coverage/prettify.js
    name: B
    signature: B(S,U,W,T)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:ece06b95
    "@type": function
    title: o
    description: Function o(S) in packages/code-parser-lib/coverage/prettify.js
    filePath: packages/code-parser-lib/coverage/prettify.js
    name: o
    signature: o(S)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:907e7c55
    "@type": function
    title: g
    description: Function g(U,T) in packages/code-parser-lib/coverage/prettify.js
    filePath: packages/code-parser-lib/coverage/prettify.js
    name: g
    signature: g(U,T)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:e524ba15
    "@type": function
    title: anonymous
    description: Function  in packages/code-parser-lib/coverage/prettify.js
    filePath: packages/code-parser-lib/coverage/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:e524ba15
    "@type": function
    title: anonymous
    description: Function  in packages/code-parser-lib/coverage/prettify.js
    filePath: packages/code-parser-lib/coverage/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:3c9268a1
    "@type": function
    title: i
    description: Function i(T) in packages/code-parser-lib/coverage/prettify.js
    filePath: packages/code-parser-lib/coverage/prettify.js
    name: i
    signature: i(T)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:73c1938f
    "@type": function
    title: Q
    description: Function Q(V,ag) in packages/code-parser-lib/coverage/prettify.js
    filePath: packages/code-parser-lib/coverage/prettify.js
    name: Q
    signature: Q(V,ag)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:fdaf44c2
    "@type": function
    title: ae
    description: Function ae(al) in packages/code-parser-lib/coverage/prettify.js
    filePath: packages/code-parser-lib/coverage/prettify.js
    name: ae
    signature: ae(al)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:77dd7299
    "@type": function
    title: ad
    description: Function ad(ak) in packages/code-parser-lib/coverage/prettify.js
    filePath: packages/code-parser-lib/coverage/prettify.js
    name: ad
    signature: ad(ak)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:0b266676
    "@type": function
    title: ai
    description: Function ai(al,ar) in packages/code-parser-lib/coverage/prettify.js
    filePath: packages/code-parser-lib/coverage/prettify.js
    name: ai
    signature: ai(al,ar)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:e5e3789d
    "@type": function
    title: D
    description: Function D(ac) in packages/code-parser-lib/coverage/prettify.js
    filePath: packages/code-parser-lib/coverage/prettify.js
    name: D
    signature: D(ac)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:88c45617
    "@type": function
    title: c
    description: Function c(U,V) in packages/code-parser-lib/coverage/prettify.js
    filePath: packages/code-parser-lib/coverage/prettify.js
    name: c
    signature: c(U,V)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:5eb71c32
    "@type": function
    title: q
    description: Function q(T,S) in packages/code-parser-lib/coverage/prettify.js
    filePath: packages/code-parser-lib/coverage/prettify.js
    name: q
    signature: q(T,S)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:4b5c9080
    "@type": function
    title: d
    description: Function d(V) in packages/code-parser-lib/coverage/prettify.js
    filePath: packages/code-parser-lib/coverage/prettify.js
    name: d
    signature: d(V)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:0b665657
    "@type": function
    title: y
    description: Function y(W,V,U) in packages/code-parser-lib/coverage/prettify.js
    filePath: packages/code-parser-lib/coverage/prettify.js
    name: y
    signature: y(W,V,U)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:f6e84c46
    "@type": function
    title: b
    description: Function b(ad) in packages/code-parser-lib/coverage/prettify.js
    filePath: packages/code-parser-lib/coverage/prettify.js
    name: b
    signature: b(ad)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:8d3ed4a3
    "@type": function
    title: Y
    description: Function Y(af) in packages/code-parser-lib/coverage/prettify.js
    filePath: packages/code-parser-lib/coverage/prettify.js
    name: Y
    signature: Y(af)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:e524ba15
    "@type": function
    title: anonymous
    description: Function  in packages/code-parser-lib/coverage/prettify.js
    filePath: packages/code-parser-lib/coverage/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:4311f862
    "@type": function
    title: U
    description: Function U() in packages/code-parser-lib/coverage/prettify.js
    filePath: packages/code-parser-lib/coverage/prettify.js
    name: U
    signature: U()
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:23b20237
    "@type": function
    title: anonymous
    description: Function  in packages/code-parser-lib/coverage/sorter.js
    filePath: packages/code-parser-lib/coverage/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 194
  - "@id": function:518cd5e2
    "@type": function
    title: getTable
    description: Function getTable() in packages/code-parser-lib/coverage/sorter.js
    filePath: packages/code-parser-lib/coverage/sorter.js
    name: getTable
    signature: getTable()
    lang: javascript
    line_start: 11
    line_end: 13
  - "@id": function:111d25a1
    "@type": function
    title: getTableHeader
    description: Function getTableHeader() in packages/code-parser-lib/coverage/sorter.js
    filePath: packages/code-parser-lib/coverage/sorter.js
    name: getTableHeader
    signature: getTableHeader()
    lang: javascript
    line_start: 15
    line_end: 17
  - "@id": function:ce4084ce
    "@type": function
    title: getTableBody
    description: Function getTableBody() in packages/code-parser-lib/coverage/sorter.js
    filePath: packages/code-parser-lib/coverage/sorter.js
    name: getTableBody
    signature: getTableBody()
    lang: javascript
    line_start: 19
    line_end: 21
  - "@id": function:c342715f
    "@type": function
    title: getNthColumn
    description: Function getNthColumn(n) in packages/code-parser-lib/coverage/sorter.js
    filePath: packages/code-parser-lib/coverage/sorter.js
    name: getNthColumn
    signature: getNthColumn(n)
    lang: javascript
    line_start: 23
    line_end: 25
  - "@id": function:cd8022e1
    "@type": function
    title: onFilterInput
    description: Function onFilterInput() in packages/code-parser-lib/coverage/sorter.js
    filePath: packages/code-parser-lib/coverage/sorter.js
    name: onFilterInput
    signature: onFilterInput()
    lang: javascript
    line_start: 27
    line_end: 42
  - "@id": function:7159af20
    "@type": function
    title: addSearchBox
    description: Function addSearchBox() in packages/code-parser-lib/coverage/sorter.js
    filePath: packages/code-parser-lib/coverage/sorter.js
    name: addSearchBox
    signature: addSearchBox()
    lang: javascript
    line_start: 45
    line_end: 50
  - "@id": function:85ffcc62
    "@type": function
    title: loadColumns
    description: Function loadColumns() in packages/code-parser-lib/coverage/sorter.js
    filePath: packages/code-parser-lib/coverage/sorter.js
    name: loadColumns
    signature: loadColumns()
    lang: javascript
    line_start: 53
    line_end: 75
  - "@id": function:e3f83c45
    "@type": function
    title: loadRowData
    description: Function loadRowData(tableRow) in
      packages/code-parser-lib/coverage/sorter.js
    filePath: packages/code-parser-lib/coverage/sorter.js
    name: loadRowData
    signature: loadRowData(tableRow)
    lang: javascript
    line_start: 78
    line_end: 95
  - "@id": function:dc04e9a8
    "@type": function
    title: loadData
    description: Function loadData() in packages/code-parser-lib/coverage/sorter.js
    filePath: packages/code-parser-lib/coverage/sorter.js
    name: loadData
    signature: loadData()
    lang: javascript
    line_start: 97
    line_end: 104
  - "@id": function:f533c0d6
    "@type": function
    title: sortByIndex
    description: Function sortByIndex(index, desc) in
      packages/code-parser-lib/coverage/sorter.js
    filePath: packages/code-parser-lib/coverage/sorter.js
    name: sortByIndex
    signature: sortByIndex(index, desc)
    lang: javascript
    line_start: 106
    line_end: 135
  - "@id": function:cb153406
    "@type": function
    title: anonymous
    description: Function  in packages/code-parser-lib/coverage/sorter.js
    filePath: packages/code-parser-lib/coverage/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 108
    line_end: 112
  - "@id": function:1852f436
    "@type": function
    title: anonymous
    description: Function  in packages/code-parser-lib/coverage/sorter.js
    filePath: packages/code-parser-lib/coverage/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 120
    line_end: 122
  - "@id": function:39e09b2d
    "@type": function
    title: removeSortIndicators
    description: Function removeSortIndicators() in
      packages/code-parser-lib/coverage/sorter.js
    filePath: packages/code-parser-lib/coverage/sorter.js
    name: removeSortIndicators
    signature: removeSortIndicators()
    lang: javascript
    line_start: 137
    line_end: 143
  - "@id": function:06d23133
    "@type": function
    title: addSortIndicators
    description: Function addSortIndicators() in packages/code-parser-lib/coverage/sorter.js
    filePath: packages/code-parser-lib/coverage/sorter.js
    name: addSortIndicators
    signature: addSortIndicators()
    lang: javascript
    line_start: 145
    line_end: 149
  - "@id": function:e10449db
    "@type": function
    title: enableUI
    description: Function enableUI() in packages/code-parser-lib/coverage/sorter.js
    filePath: packages/code-parser-lib/coverage/sorter.js
    name: enableUI
    signature: enableUI()
    lang: javascript
    line_start: 151
    line_end: 182
  - "@id": function:ee8a62c6
    "@type": function
    title: anonymous
    description: Function  in packages/code-parser-lib/coverage/sorter.js
    filePath: packages/code-parser-lib/coverage/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 154
    line_end: 169
  - "@id": function:3989e702
    "@type": function
    title: anonymous
    description: Function  in packages/code-parser-lib/coverage/sorter.js
    filePath: packages/code-parser-lib/coverage/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 157
    line_end: 168
  - "@id": function:34fd8618
    "@type": function
    title: anonymous
    description: Function  in packages/code-parser-lib/coverage/sorter.js
    filePath: packages/code-parser-lib/coverage/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 184
    line_end: 193
  - "@id": function:d00970ac
    "@type": function
    title: add
    description: Function add(a, b) in
      packages/code-parser-lib/tests/fixtures/javascript/hello.js
    filePath: packages/code-parser-lib/tests/fixtures/javascript/hello.js
    name: add
    signature: add(a, b)
    lang: javascript
    line_start: 6
    line_end: 8
  - "@id": function:3e3c815f
    "@type": function
    title: multiply
    description: Function multiply(x, y) in
      packages/code-parser-lib/tests/fixtures/javascript/hello.js
    filePath: packages/code-parser-lib/tests/fixtures/javascript/hello.js
    name: multiply
    signature: multiply(x, y)
    lang: javascript
    line_start: 10
    line_end: 12
  - "@id": function:70e4492b
    "@type": function
    title: calculateArea
    description: Function calculateArea(length, width) in
      packages/code-parser-lib/tests/fixtures/javascript/hello.js
    filePath: packages/code-parser-lib/tests/fixtures/javascript/hello.js
    name: calculateArea
    signature: calculateArea(length, width)
    lang: javascript
    line_start: 14
    line_end: 16
  - "@id": function:e706b452
    "@type": function
    title: anonymous
    description: Function ((name)) => in
      packages/code-parser-lib/tests/fixtures/javascript/hello.js
    filePath: packages/code-parser-lib/tests/fixtures/javascript/hello.js
    name: anonymous
    signature: ((name)) =>
    lang: javascript
    line_start: 18
    line_end: 20
  - "@id": function:c831252e
    "@type": function
    title: processData
    description: Function processData(data) in
      packages/code-parser-lib/tests/fixtures/javascript/hello.js
    filePath: packages/code-parser-lib/tests/fixtures/javascript/hello.js
    name: processData
    signature: processData(data)
    lang: javascript
    line_start: 22
    line_end: 25
  - "@id": function:2c186ca4
    "@type": function
    title: anonymous
    description: Function (item) => in
      packages/code-parser-lib/tests/fixtures/javascript/hello.js
    filePath: packages/code-parser-lib/tests/fixtures/javascript/hello.js
    name: anonymous
    signature: (item) =>
    lang: javascript
    line_start: 23
    line_end: 23
  - "@id": function:373bb90f
    "@type": function
    title: constructor
    description: Function constructor() in
      packages/code-parser-lib/tests/fixtures/javascript/hello.js
    filePath: packages/code-parser-lib/tests/fixtures/javascript/hello.js
    name: constructor
    signature: constructor()
    lang: javascript
    line_start: 28
    line_end: 30
  - "@id": function:cb75dd81
    "@type": function
    title: addNumbers
    description: Function addNumbers(a, b) in
      packages/code-parser-lib/tests/fixtures/javascript/hello.js
    filePath: packages/code-parser-lib/tests/fixtures/javascript/hello.js
    name: addNumbers
    signature: addNumbers(a, b)
    lang: javascript
    line_start: 32
    line_end: 36
  - "@id": function:b5172ccc
    "@type": function
    title: multiplyNumbers
    description: Function multiplyNumbers(x, y) in
      packages/code-parser-lib/tests/fixtures/javascript/hello.js
    filePath: packages/code-parser-lib/tests/fixtures/javascript/hello.js
    name: multiplyNumbers
    signature: multiplyNumbers(x, y)
    lang: javascript
    line_start: 38
    line_end: 42
  - "@id": function:4f3d35ee
    "@type": function
    title: main
    description: Function main() in
      packages/code-parser-lib/tests/fixtures/javascript/hello.js
    filePath: packages/code-parser-lib/tests/fixtures/javascript/hello.js
    name: main
    signature: main()
    lang: javascript
    line_start: 45
    line_end: 74
  - "@id": function:bfe7ae8a
    "@type": function
    title: add
    description: Function add(a, b) in
      packages/code-parser-lib/tests/fixtures/python/hello.py
    filePath: packages/code-parser-lib/tests/fixtures/python/hello.py
    name: add
    signature: add(a, b)
    lang: python
    line_start: 7
    line_end: 9
  - "@id": function:d0c1ee84
    "@type": function
    title: multiply
    description: Function multiply(x, y) in
      packages/code-parser-lib/tests/fixtures/python/hello.py
    filePath: packages/code-parser-lib/tests/fixtures/python/hello.py
    name: multiply
    signature: multiply(x, y)
    lang: python
    line_start: 11
    line_end: 13
  - "@id": function:d711b6cf
    "@type": function
    title: calculate_area
    description: Function calculate_area(length, width) in
      packages/code-parser-lib/tests/fixtures/python/hello.py
    filePath: packages/code-parser-lib/tests/fixtures/python/hello.py
    name: calculate_area
    signature: calculate_area(length, width)
    lang: python
    line_start: 15
    line_end: 17
  - "@id": function:d19cbd09
    "@type": function
    title: greet
    description: Function greet(name) in
      packages/code-parser-lib/tests/fixtures/python/hello.py
    filePath: packages/code-parser-lib/tests/fixtures/python/hello.py
    name: greet
    signature: greet(name)
    lang: python
    line_start: 19
    line_end: 21
  - "@id": function:0133bd6b
    "@type": function
    title: main
    description: Function main() in packages/code-parser-lib/tests/fixtures/python/hello.py
    filePath: packages/code-parser-lib/tests/fixtures/python/hello.py
    name: main
    signature: main()
    lang: python
    line_start: 23
    line_end: 40
  - "@id": function:89077f38
    "@type": function
    title: anonymous
    description: Function  in packages/kg-cli/coverage/block-navigation.js
    filePath: packages/kg-cli/coverage/block-navigation.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 86
  - "@id": function:181cf731
    "@type": function
    title: toggleClass
    description: Function toggleClass(index) in packages/kg-cli/coverage/block-navigation.js
    filePath: packages/kg-cli/coverage/block-navigation.js
    name: toggleClass
    signature: toggleClass(index)
    lang: javascript
    line_start: 24
    line_end: 29
  - "@id": function:6f1f0c0b
    "@type": function
    title: makeCurrent
    description: Function makeCurrent(index) in packages/kg-cli/coverage/block-navigation.js
    filePath: packages/kg-cli/coverage/block-navigation.js
    name: makeCurrent
    signature: makeCurrent(index)
    lang: javascript
    line_start: 31
    line_end: 39
  - "@id": function:1fe5819e
    "@type": function
    title: goToPrevious
    description: Function goToPrevious() in packages/kg-cli/coverage/block-navigation.js
    filePath: packages/kg-cli/coverage/block-navigation.js
    name: goToPrevious
    signature: goToPrevious()
    lang: javascript
    line_start: 41
    line_end: 50
  - "@id": function:0cd408f8
    "@type": function
    title: goToNext
    description: Function goToNext() in packages/kg-cli/coverage/block-navigation.js
    filePath: packages/kg-cli/coverage/block-navigation.js
    name: goToNext
    signature: goToNext()
    lang: javascript
    line_start: 52
    line_end: 63
  - "@id": function:25d05e27
    "@type": function
    title: anonymous
    description: Function  in packages/kg-cli/coverage/block-navigation.js
    filePath: packages/kg-cli/coverage/block-navigation.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 65
    line_end: 85
  - "@id": function:9c52c697
    "@type": function
    title: anonymous
    description: Function  in packages/kg-cli/coverage/lcov-report/block-navigation.js
    filePath: packages/kg-cli/coverage/lcov-report/block-navigation.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 86
  - "@id": function:2f219ac8
    "@type": function
    title: toggleClass
    description: Function toggleClass(index) in
      packages/kg-cli/coverage/lcov-report/block-navigation.js
    filePath: packages/kg-cli/coverage/lcov-report/block-navigation.js
    name: toggleClass
    signature: toggleClass(index)
    lang: javascript
    line_start: 24
    line_end: 29
  - "@id": function:c4425cda
    "@type": function
    title: makeCurrent
    description: Function makeCurrent(index) in
      packages/kg-cli/coverage/lcov-report/block-navigation.js
    filePath: packages/kg-cli/coverage/lcov-report/block-navigation.js
    name: makeCurrent
    signature: makeCurrent(index)
    lang: javascript
    line_start: 31
    line_end: 39
  - "@id": function:20f0d49c
    "@type": function
    title: goToPrevious
    description: Function goToPrevious() in
      packages/kg-cli/coverage/lcov-report/block-navigation.js
    filePath: packages/kg-cli/coverage/lcov-report/block-navigation.js
    name: goToPrevious
    signature: goToPrevious()
    lang: javascript
    line_start: 41
    line_end: 50
  - "@id": function:45d2aefc
    "@type": function
    title: goToNext
    description: Function goToNext() in
      packages/kg-cli/coverage/lcov-report/block-navigation.js
    filePath: packages/kg-cli/coverage/lcov-report/block-navigation.js
    name: goToNext
    signature: goToNext()
    lang: javascript
    line_start: 52
    line_end: 63
  - "@id": function:25d39067
    "@type": function
    title: anonymous
    description: Function  in packages/kg-cli/coverage/lcov-report/block-navigation.js
    filePath: packages/kg-cli/coverage/lcov-report/block-navigation.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 65
    line_end: 85
  - "@id": function:faa7bf35
    "@type": function
    title: anonymous
    description: Function  in packages/kg-cli/coverage/lcov-report/prettify.js
    filePath: packages/kg-cli/coverage/lcov-report/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:7e03d1e7
    "@type": function
    title: k
    description: Function k(Z) in packages/kg-cli/coverage/lcov-report/prettify.js
    filePath: packages/kg-cli/coverage/lcov-report/prettify.js
    name: k
    signature: k(Z)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:51038a40
    "@type": function
    title: ab
    description: Function ab(ah) in packages/kg-cli/coverage/lcov-report/prettify.js
    filePath: packages/kg-cli/coverage/lcov-report/prettify.js
    name: ab
    signature: ab(ah)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:bb44441b
    "@type": function
    title: T
    description: Function T(af) in packages/kg-cli/coverage/lcov-report/prettify.js
    filePath: packages/kg-cli/coverage/lcov-report/prettify.js
    name: T
    signature: T(af)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:2ffec5e5
    "@type": function
    title: X
    description: Function X(am) in packages/kg-cli/coverage/lcov-report/prettify.js
    filePath: packages/kg-cli/coverage/lcov-report/prettify.js
    name: X
    signature: X(am)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:faa7bf35
    "@type": function
    title: anonymous
    description: Function  in packages/kg-cli/coverage/lcov-report/prettify.js
    filePath: packages/kg-cli/coverage/lcov-report/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:23a98c52
    "@type": function
    title: W
    description: Function W(al) in packages/kg-cli/coverage/lcov-report/prettify.js
    filePath: packages/kg-cli/coverage/lcov-report/prettify.js
    name: W
    signature: W(al)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:faa7bf35
    "@type": function
    title: anonymous
    description: Function  in packages/kg-cli/coverage/lcov-report/prettify.js
    filePath: packages/kg-cli/coverage/lcov-report/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:38409e9c
    "@type": function
    title: a
    description: Function a(V) in packages/kg-cli/coverage/lcov-report/prettify.js
    filePath: packages/kg-cli/coverage/lcov-report/prettify.js
    name: a
    signature: a(V)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:68ac86ff
    "@type": function
    title: aa
    description: Function aa(ab) in packages/kg-cli/coverage/lcov-report/prettify.js
    filePath: packages/kg-cli/coverage/lcov-report/prettify.js
    name: aa
    signature: aa(ab)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:8ac21559
    "@type": function
    title: B
    description: Function B(S,U,W,T) in packages/kg-cli/coverage/lcov-report/prettify.js
    filePath: packages/kg-cli/coverage/lcov-report/prettify.js
    name: B
    signature: B(S,U,W,T)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:b1081e77
    "@type": function
    title: o
    description: Function o(S) in packages/kg-cli/coverage/lcov-report/prettify.js
    filePath: packages/kg-cli/coverage/lcov-report/prettify.js
    name: o
    signature: o(S)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:bf8ae5f6
    "@type": function
    title: g
    description: Function g(U,T) in packages/kg-cli/coverage/lcov-report/prettify.js
    filePath: packages/kg-cli/coverage/lcov-report/prettify.js
    name: g
    signature: g(U,T)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:faa7bf35
    "@type": function
    title: anonymous
    description: Function  in packages/kg-cli/coverage/lcov-report/prettify.js
    filePath: packages/kg-cli/coverage/lcov-report/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:faa7bf35
    "@type": function
    title: anonymous
    description: Function  in packages/kg-cli/coverage/lcov-report/prettify.js
    filePath: packages/kg-cli/coverage/lcov-report/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:810ab81d
    "@type": function
    title: i
    description: Function i(T) in packages/kg-cli/coverage/lcov-report/prettify.js
    filePath: packages/kg-cli/coverage/lcov-report/prettify.js
    name: i
    signature: i(T)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:6863ebf5
    "@type": function
    title: Q
    description: Function Q(V,ag) in packages/kg-cli/coverage/lcov-report/prettify.js
    filePath: packages/kg-cli/coverage/lcov-report/prettify.js
    name: Q
    signature: Q(V,ag)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:fce37fbb
    "@type": function
    title: ae
    description: Function ae(al) in packages/kg-cli/coverage/lcov-report/prettify.js
    filePath: packages/kg-cli/coverage/lcov-report/prettify.js
    name: ae
    signature: ae(al)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:18e5ea15
    "@type": function
    title: ad
    description: Function ad(ak) in packages/kg-cli/coverage/lcov-report/prettify.js
    filePath: packages/kg-cli/coverage/lcov-report/prettify.js
    name: ad
    signature: ad(ak)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:1d8075af
    "@type": function
    title: ai
    description: Function ai(al,ar) in packages/kg-cli/coverage/lcov-report/prettify.js
    filePath: packages/kg-cli/coverage/lcov-report/prettify.js
    name: ai
    signature: ai(al,ar)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:f38a9806
    "@type": function
    title: D
    description: Function D(ac) in packages/kg-cli/coverage/lcov-report/prettify.js
    filePath: packages/kg-cli/coverage/lcov-report/prettify.js
    name: D
    signature: D(ac)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:a05a3f1b
    "@type": function
    title: c
    description: Function c(U,V) in packages/kg-cli/coverage/lcov-report/prettify.js
    filePath: packages/kg-cli/coverage/lcov-report/prettify.js
    name: c
    signature: c(U,V)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:960271dd
    "@type": function
    title: q
    description: Function q(T,S) in packages/kg-cli/coverage/lcov-report/prettify.js
    filePath: packages/kg-cli/coverage/lcov-report/prettify.js
    name: q
    signature: q(T,S)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:1ecad89c
    "@type": function
    title: d
    description: Function d(V) in packages/kg-cli/coverage/lcov-report/prettify.js
    filePath: packages/kg-cli/coverage/lcov-report/prettify.js
    name: d
    signature: d(V)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:6054a91d
    "@type": function
    title: y
    description: Function y(W,V,U) in packages/kg-cli/coverage/lcov-report/prettify.js
    filePath: packages/kg-cli/coverage/lcov-report/prettify.js
    name: y
    signature: y(W,V,U)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:84782c13
    "@type": function
    title: b
    description: Function b(ad) in packages/kg-cli/coverage/lcov-report/prettify.js
    filePath: packages/kg-cli/coverage/lcov-report/prettify.js
    name: b
    signature: b(ad)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:fe7e632b
    "@type": function
    title: Y
    description: Function Y(af) in packages/kg-cli/coverage/lcov-report/prettify.js
    filePath: packages/kg-cli/coverage/lcov-report/prettify.js
    name: Y
    signature: Y(af)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:faa7bf35
    "@type": function
    title: anonymous
    description: Function  in packages/kg-cli/coverage/lcov-report/prettify.js
    filePath: packages/kg-cli/coverage/lcov-report/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:ab5f1451
    "@type": function
    title: U
    description: Function U() in packages/kg-cli/coverage/lcov-report/prettify.js
    filePath: packages/kg-cli/coverage/lcov-report/prettify.js
    name: U
    signature: U()
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:69ba4210
    "@type": function
    title: anonymous
    description: Function  in packages/kg-cli/coverage/lcov-report/sorter.js
    filePath: packages/kg-cli/coverage/lcov-report/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 194
  - "@id": function:96cc2c87
    "@type": function
    title: getTable
    description: Function getTable() in packages/kg-cli/coverage/lcov-report/sorter.js
    filePath: packages/kg-cli/coverage/lcov-report/sorter.js
    name: getTable
    signature: getTable()
    lang: javascript
    line_start: 11
    line_end: 13
  - "@id": function:8bfe4d5e
    "@type": function
    title: getTableHeader
    description: Function getTableHeader() in packages/kg-cli/coverage/lcov-report/sorter.js
    filePath: packages/kg-cli/coverage/lcov-report/sorter.js
    name: getTableHeader
    signature: getTableHeader()
    lang: javascript
    line_start: 15
    line_end: 17
  - "@id": function:7ab44ce1
    "@type": function
    title: getTableBody
    description: Function getTableBody() in packages/kg-cli/coverage/lcov-report/sorter.js
    filePath: packages/kg-cli/coverage/lcov-report/sorter.js
    name: getTableBody
    signature: getTableBody()
    lang: javascript
    line_start: 19
    line_end: 21
  - "@id": function:4585cf6f
    "@type": function
    title: getNthColumn
    description: Function getNthColumn(n) in packages/kg-cli/coverage/lcov-report/sorter.js
    filePath: packages/kg-cli/coverage/lcov-report/sorter.js
    name: getNthColumn
    signature: getNthColumn(n)
    lang: javascript
    line_start: 23
    line_end: 25
  - "@id": function:659cdbc4
    "@type": function
    title: onFilterInput
    description: Function onFilterInput() in packages/kg-cli/coverage/lcov-report/sorter.js
    filePath: packages/kg-cli/coverage/lcov-report/sorter.js
    name: onFilterInput
    signature: onFilterInput()
    lang: javascript
    line_start: 27
    line_end: 42
  - "@id": function:ecce2e28
    "@type": function
    title: addSearchBox
    description: Function addSearchBox() in packages/kg-cli/coverage/lcov-report/sorter.js
    filePath: packages/kg-cli/coverage/lcov-report/sorter.js
    name: addSearchBox
    signature: addSearchBox()
    lang: javascript
    line_start: 45
    line_end: 50
  - "@id": function:ab753622
    "@type": function
    title: loadColumns
    description: Function loadColumns() in packages/kg-cli/coverage/lcov-report/sorter.js
    filePath: packages/kg-cli/coverage/lcov-report/sorter.js
    name: loadColumns
    signature: loadColumns()
    lang: javascript
    line_start: 53
    line_end: 75
  - "@id": function:b94a3090
    "@type": function
    title: loadRowData
    description: Function loadRowData(tableRow) in
      packages/kg-cli/coverage/lcov-report/sorter.js
    filePath: packages/kg-cli/coverage/lcov-report/sorter.js
    name: loadRowData
    signature: loadRowData(tableRow)
    lang: javascript
    line_start: 78
    line_end: 95
  - "@id": function:7fcd8c48
    "@type": function
    title: loadData
    description: Function loadData() in packages/kg-cli/coverage/lcov-report/sorter.js
    filePath: packages/kg-cli/coverage/lcov-report/sorter.js
    name: loadData
    signature: loadData()
    lang: javascript
    line_start: 97
    line_end: 104
  - "@id": function:8bb97a6f
    "@type": function
    title: sortByIndex
    description: Function sortByIndex(index, desc) in
      packages/kg-cli/coverage/lcov-report/sorter.js
    filePath: packages/kg-cli/coverage/lcov-report/sorter.js
    name: sortByIndex
    signature: sortByIndex(index, desc)
    lang: javascript
    line_start: 106
    line_end: 135
  - "@id": function:cd21c2c0
    "@type": function
    title: anonymous
    description: Function  in packages/kg-cli/coverage/lcov-report/sorter.js
    filePath: packages/kg-cli/coverage/lcov-report/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 108
    line_end: 112
  - "@id": function:080c0678
    "@type": function
    title: anonymous
    description: Function  in packages/kg-cli/coverage/lcov-report/sorter.js
    filePath: packages/kg-cli/coverage/lcov-report/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 120
    line_end: 122
  - "@id": function:dcdd79f7
    "@type": function
    title: removeSortIndicators
    description: Function removeSortIndicators() in
      packages/kg-cli/coverage/lcov-report/sorter.js
    filePath: packages/kg-cli/coverage/lcov-report/sorter.js
    name: removeSortIndicators
    signature: removeSortIndicators()
    lang: javascript
    line_start: 137
    line_end: 143
  - "@id": function:155f8747
    "@type": function
    title: addSortIndicators
    description: Function addSortIndicators() in
      packages/kg-cli/coverage/lcov-report/sorter.js
    filePath: packages/kg-cli/coverage/lcov-report/sorter.js
    name: addSortIndicators
    signature: addSortIndicators()
    lang: javascript
    line_start: 145
    line_end: 149
  - "@id": function:b456d0cf
    "@type": function
    title: enableUI
    description: Function enableUI() in packages/kg-cli/coverage/lcov-report/sorter.js
    filePath: packages/kg-cli/coverage/lcov-report/sorter.js
    name: enableUI
    signature: enableUI()
    lang: javascript
    line_start: 151
    line_end: 182
  - "@id": function:79d2413e
    "@type": function
    title: anonymous
    description: Function  in packages/kg-cli/coverage/lcov-report/sorter.js
    filePath: packages/kg-cli/coverage/lcov-report/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 154
    line_end: 169
  - "@id": function:c7b8627c
    "@type": function
    title: anonymous
    description: Function  in packages/kg-cli/coverage/lcov-report/sorter.js
    filePath: packages/kg-cli/coverage/lcov-report/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 157
    line_end: 168
  - "@id": function:fc81caba
    "@type": function
    title: anonymous
    description: Function  in packages/kg-cli/coverage/lcov-report/sorter.js
    filePath: packages/kg-cli/coverage/lcov-report/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 184
    line_end: 193
  - "@id": function:4d951c43
    "@type": function
    title: anonymous
    description: Function  in packages/kg-cli/coverage/prettify.js
    filePath: packages/kg-cli/coverage/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:4f1512ba
    "@type": function
    title: k
    description: Function k(Z) in packages/kg-cli/coverage/prettify.js
    filePath: packages/kg-cli/coverage/prettify.js
    name: k
    signature: k(Z)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:eb2d1fab
    "@type": function
    title: ab
    description: Function ab(ah) in packages/kg-cli/coverage/prettify.js
    filePath: packages/kg-cli/coverage/prettify.js
    name: ab
    signature: ab(ah)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:611aa608
    "@type": function
    title: T
    description: Function T(af) in packages/kg-cli/coverage/prettify.js
    filePath: packages/kg-cli/coverage/prettify.js
    name: T
    signature: T(af)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:94d2ade2
    "@type": function
    title: X
    description: Function X(am) in packages/kg-cli/coverage/prettify.js
    filePath: packages/kg-cli/coverage/prettify.js
    name: X
    signature: X(am)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:4d951c43
    "@type": function
    title: anonymous
    description: Function  in packages/kg-cli/coverage/prettify.js
    filePath: packages/kg-cli/coverage/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:0e71f83e
    "@type": function
    title: W
    description: Function W(al) in packages/kg-cli/coverage/prettify.js
    filePath: packages/kg-cli/coverage/prettify.js
    name: W
    signature: W(al)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:4d951c43
    "@type": function
    title: anonymous
    description: Function  in packages/kg-cli/coverage/prettify.js
    filePath: packages/kg-cli/coverage/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:c8b44991
    "@type": function
    title: a
    description: Function a(V) in packages/kg-cli/coverage/prettify.js
    filePath: packages/kg-cli/coverage/prettify.js
    name: a
    signature: a(V)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:262222d7
    "@type": function
    title: aa
    description: Function aa(ab) in packages/kg-cli/coverage/prettify.js
    filePath: packages/kg-cli/coverage/prettify.js
    name: aa
    signature: aa(ab)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:979397a0
    "@type": function
    title: B
    description: Function B(S,U,W,T) in packages/kg-cli/coverage/prettify.js
    filePath: packages/kg-cli/coverage/prettify.js
    name: B
    signature: B(S,U,W,T)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:96951bf5
    "@type": function
    title: o
    description: Function o(S) in packages/kg-cli/coverage/prettify.js
    filePath: packages/kg-cli/coverage/prettify.js
    name: o
    signature: o(S)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:b43e68f2
    "@type": function
    title: g
    description: Function g(U,T) in packages/kg-cli/coverage/prettify.js
    filePath: packages/kg-cli/coverage/prettify.js
    name: g
    signature: g(U,T)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:4d951c43
    "@type": function
    title: anonymous
    description: Function  in packages/kg-cli/coverage/prettify.js
    filePath: packages/kg-cli/coverage/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:4d951c43
    "@type": function
    title: anonymous
    description: Function  in packages/kg-cli/coverage/prettify.js
    filePath: packages/kg-cli/coverage/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:0daa86d3
    "@type": function
    title: i
    description: Function i(T) in packages/kg-cli/coverage/prettify.js
    filePath: packages/kg-cli/coverage/prettify.js
    name: i
    signature: i(T)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:f143643e
    "@type": function
    title: Q
    description: Function Q(V,ag) in packages/kg-cli/coverage/prettify.js
    filePath: packages/kg-cli/coverage/prettify.js
    name: Q
    signature: Q(V,ag)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:99ff8069
    "@type": function
    title: ae
    description: Function ae(al) in packages/kg-cli/coverage/prettify.js
    filePath: packages/kg-cli/coverage/prettify.js
    name: ae
    signature: ae(al)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:35469f5e
    "@type": function
    title: ad
    description: Function ad(ak) in packages/kg-cli/coverage/prettify.js
    filePath: packages/kg-cli/coverage/prettify.js
    name: ad
    signature: ad(ak)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:e971d614
    "@type": function
    title: ai
    description: Function ai(al,ar) in packages/kg-cli/coverage/prettify.js
    filePath: packages/kg-cli/coverage/prettify.js
    name: ai
    signature: ai(al,ar)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:bf11f81b
    "@type": function
    title: D
    description: Function D(ac) in packages/kg-cli/coverage/prettify.js
    filePath: packages/kg-cli/coverage/prettify.js
    name: D
    signature: D(ac)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:b60afdb1
    "@type": function
    title: c
    description: Function c(U,V) in packages/kg-cli/coverage/prettify.js
    filePath: packages/kg-cli/coverage/prettify.js
    name: c
    signature: c(U,V)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:50e7d45c
    "@type": function
    title: q
    description: Function q(T,S) in packages/kg-cli/coverage/prettify.js
    filePath: packages/kg-cli/coverage/prettify.js
    name: q
    signature: q(T,S)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:b1beca04
    "@type": function
    title: d
    description: Function d(V) in packages/kg-cli/coverage/prettify.js
    filePath: packages/kg-cli/coverage/prettify.js
    name: d
    signature: d(V)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:ea857938
    "@type": function
    title: y
    description: Function y(W,V,U) in packages/kg-cli/coverage/prettify.js
    filePath: packages/kg-cli/coverage/prettify.js
    name: y
    signature: y(W,V,U)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:b2229f8c
    "@type": function
    title: b
    description: Function b(ad) in packages/kg-cli/coverage/prettify.js
    filePath: packages/kg-cli/coverage/prettify.js
    name: b
    signature: b(ad)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:084d0bdf
    "@type": function
    title: Y
    description: Function Y(af) in packages/kg-cli/coverage/prettify.js
    filePath: packages/kg-cli/coverage/prettify.js
    name: Y
    signature: Y(af)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:4d951c43
    "@type": function
    title: anonymous
    description: Function  in packages/kg-cli/coverage/prettify.js
    filePath: packages/kg-cli/coverage/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:469f4a4d
    "@type": function
    title: U
    description: Function U() in packages/kg-cli/coverage/prettify.js
    filePath: packages/kg-cli/coverage/prettify.js
    name: U
    signature: U()
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:f35d5879
    "@type": function
    title: anonymous
    description: Function  in packages/kg-cli/coverage/sorter.js
    filePath: packages/kg-cli/coverage/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 194
  - "@id": function:62fd7799
    "@type": function
    title: getTable
    description: Function getTable() in packages/kg-cli/coverage/sorter.js
    filePath: packages/kg-cli/coverage/sorter.js
    name: getTable
    signature: getTable()
    lang: javascript
    line_start: 11
    line_end: 13
  - "@id": function:9b39335e
    "@type": function
    title: getTableHeader
    description: Function getTableHeader() in packages/kg-cli/coverage/sorter.js
    filePath: packages/kg-cli/coverage/sorter.js
    name: getTableHeader
    signature: getTableHeader()
    lang: javascript
    line_start: 15
    line_end: 17
  - "@id": function:5acfcbec
    "@type": function
    title: getTableBody
    description: Function getTableBody() in packages/kg-cli/coverage/sorter.js
    filePath: packages/kg-cli/coverage/sorter.js
    name: getTableBody
    signature: getTableBody()
    lang: javascript
    line_start: 19
    line_end: 21
  - "@id": function:ec34b45b
    "@type": function
    title: getNthColumn
    description: Function getNthColumn(n) in packages/kg-cli/coverage/sorter.js
    filePath: packages/kg-cli/coverage/sorter.js
    name: getNthColumn
    signature: getNthColumn(n)
    lang: javascript
    line_start: 23
    line_end: 25
  - "@id": function:ba31e97a
    "@type": function
    title: onFilterInput
    description: Function onFilterInput() in packages/kg-cli/coverage/sorter.js
    filePath: packages/kg-cli/coverage/sorter.js
    name: onFilterInput
    signature: onFilterInput()
    lang: javascript
    line_start: 27
    line_end: 42
  - "@id": function:77a7d51a
    "@type": function
    title: addSearchBox
    description: Function addSearchBox() in packages/kg-cli/coverage/sorter.js
    filePath: packages/kg-cli/coverage/sorter.js
    name: addSearchBox
    signature: addSearchBox()
    lang: javascript
    line_start: 45
    line_end: 50
  - "@id": function:aec163de
    "@type": function
    title: loadColumns
    description: Function loadColumns() in packages/kg-cli/coverage/sorter.js
    filePath: packages/kg-cli/coverage/sorter.js
    name: loadColumns
    signature: loadColumns()
    lang: javascript
    line_start: 53
    line_end: 75
  - "@id": function:6f0bbe4a
    "@type": function
    title: loadRowData
    description: Function loadRowData(tableRow) in packages/kg-cli/coverage/sorter.js
    filePath: packages/kg-cli/coverage/sorter.js
    name: loadRowData
    signature: loadRowData(tableRow)
    lang: javascript
    line_start: 78
    line_end: 95
  - "@id": function:7b1a972e
    "@type": function
    title: loadData
    description: Function loadData() in packages/kg-cli/coverage/sorter.js
    filePath: packages/kg-cli/coverage/sorter.js
    name: loadData
    signature: loadData()
    lang: javascript
    line_start: 97
    line_end: 104
  - "@id": function:2bf48c07
    "@type": function
    title: sortByIndex
    description: Function sortByIndex(index, desc) in packages/kg-cli/coverage/sorter.js
    filePath: packages/kg-cli/coverage/sorter.js
    name: sortByIndex
    signature: sortByIndex(index, desc)
    lang: javascript
    line_start: 106
    line_end: 135
  - "@id": function:075cdf26
    "@type": function
    title: anonymous
    description: Function  in packages/kg-cli/coverage/sorter.js
    filePath: packages/kg-cli/coverage/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 108
    line_end: 112
  - "@id": function:7039fc7a
    "@type": function
    title: anonymous
    description: Function  in packages/kg-cli/coverage/sorter.js
    filePath: packages/kg-cli/coverage/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 120
    line_end: 122
  - "@id": function:490adf81
    "@type": function
    title: removeSortIndicators
    description: Function removeSortIndicators() in packages/kg-cli/coverage/sorter.js
    filePath: packages/kg-cli/coverage/sorter.js
    name: removeSortIndicators
    signature: removeSortIndicators()
    lang: javascript
    line_start: 137
    line_end: 143
  - "@id": function:5cf0369a
    "@type": function
    title: addSortIndicators
    description: Function addSortIndicators() in packages/kg-cli/coverage/sorter.js
    filePath: packages/kg-cli/coverage/sorter.js
    name: addSortIndicators
    signature: addSortIndicators()
    lang: javascript
    line_start: 145
    line_end: 149
  - "@id": function:7e25f937
    "@type": function
    title: enableUI
    description: Function enableUI() in packages/kg-cli/coverage/sorter.js
    filePath: packages/kg-cli/coverage/sorter.js
    name: enableUI
    signature: enableUI()
    lang: javascript
    line_start: 151
    line_end: 182
  - "@id": function:b77f69b5
    "@type": function
    title: anonymous
    description: Function  in packages/kg-cli/coverage/sorter.js
    filePath: packages/kg-cli/coverage/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 154
    line_end: 169
  - "@id": function:ebe733ff
    "@type": function
    title: anonymous
    description: Function  in packages/kg-cli/coverage/sorter.js
    filePath: packages/kg-cli/coverage/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 157
    line_end: 168
  - "@id": function:8fc18c91
    "@type": function
    title: anonymous
    description: Function  in packages/kg-cli/coverage/sorter.js
    filePath: packages/kg-cli/coverage/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 184
    line_end: 193
  - "@id": function:27973ef1
    "@type": function
    title: anonymous
    description: Function  in packages/kg-sync-lib/coverage/block-navigation.js
    filePath: packages/kg-sync-lib/coverage/block-navigation.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 86
  - "@id": function:f8b68537
    "@type": function
    title: toggleClass
    description: Function toggleClass(index) in
      packages/kg-sync-lib/coverage/block-navigation.js
    filePath: packages/kg-sync-lib/coverage/block-navigation.js
    name: toggleClass
    signature: toggleClass(index)
    lang: javascript
    line_start: 24
    line_end: 29
  - "@id": function:90343e73
    "@type": function
    title: makeCurrent
    description: Function makeCurrent(index) in
      packages/kg-sync-lib/coverage/block-navigation.js
    filePath: packages/kg-sync-lib/coverage/block-navigation.js
    name: makeCurrent
    signature: makeCurrent(index)
    lang: javascript
    line_start: 31
    line_end: 39
  - "@id": function:f01f626d
    "@type": function
    title: goToPrevious
    description: Function goToPrevious() in
      packages/kg-sync-lib/coverage/block-navigation.js
    filePath: packages/kg-sync-lib/coverage/block-navigation.js
    name: goToPrevious
    signature: goToPrevious()
    lang: javascript
    line_start: 41
    line_end: 50
  - "@id": function:613e1073
    "@type": function
    title: goToNext
    description: Function goToNext() in packages/kg-sync-lib/coverage/block-navigation.js
    filePath: packages/kg-sync-lib/coverage/block-navigation.js
    name: goToNext
    signature: goToNext()
    lang: javascript
    line_start: 52
    line_end: 63
  - "@id": function:d0e66114
    "@type": function
    title: anonymous
    description: Function  in packages/kg-sync-lib/coverage/block-navigation.js
    filePath: packages/kg-sync-lib/coverage/block-navigation.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 65
    line_end: 85
  - "@id": function:9603f228
    "@type": function
    title: anonymous
    description: Function  in packages/kg-sync-lib/coverage/lcov-report/block-navigation.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/block-navigation.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 86
  - "@id": function:e791b486
    "@type": function
    title: toggleClass
    description: Function toggleClass(index) in
      packages/kg-sync-lib/coverage/lcov-report/block-navigation.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/block-navigation.js
    name: toggleClass
    signature: toggleClass(index)
    lang: javascript
    line_start: 24
    line_end: 29
  - "@id": function:f271d6ad
    "@type": function
    title: makeCurrent
    description: Function makeCurrent(index) in
      packages/kg-sync-lib/coverage/lcov-report/block-navigation.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/block-navigation.js
    name: makeCurrent
    signature: makeCurrent(index)
    lang: javascript
    line_start: 31
    line_end: 39
  - "@id": function:3f89bbca
    "@type": function
    title: goToPrevious
    description: Function goToPrevious() in
      packages/kg-sync-lib/coverage/lcov-report/block-navigation.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/block-navigation.js
    name: goToPrevious
    signature: goToPrevious()
    lang: javascript
    line_start: 41
    line_end: 50
  - "@id": function:9e96d8fd
    "@type": function
    title: goToNext
    description: Function goToNext() in
      packages/kg-sync-lib/coverage/lcov-report/block-navigation.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/block-navigation.js
    name: goToNext
    signature: goToNext()
    lang: javascript
    line_start: 52
    line_end: 63
  - "@id": function:ee283fda
    "@type": function
    title: anonymous
    description: Function  in packages/kg-sync-lib/coverage/lcov-report/block-navigation.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/block-navigation.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 65
    line_end: 85
  - "@id": function:f1e13714
    "@type": function
    title: anonymous
    description: Function  in packages/kg-sync-lib/coverage/lcov-report/prettify.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:ae3c8e5d
    "@type": function
    title: k
    description: Function k(Z) in packages/kg-sync-lib/coverage/lcov-report/prettify.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    name: k
    signature: k(Z)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:a8808683
    "@type": function
    title: ab
    description: Function ab(ah) in packages/kg-sync-lib/coverage/lcov-report/prettify.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    name: ab
    signature: ab(ah)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:a8da3cba
    "@type": function
    title: T
    description: Function T(af) in packages/kg-sync-lib/coverage/lcov-report/prettify.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    name: T
    signature: T(af)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:e18c678b
    "@type": function
    title: X
    description: Function X(am) in packages/kg-sync-lib/coverage/lcov-report/prettify.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    name: X
    signature: X(am)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:f1e13714
    "@type": function
    title: anonymous
    description: Function  in packages/kg-sync-lib/coverage/lcov-report/prettify.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:e8c8ef11
    "@type": function
    title: W
    description: Function W(al) in packages/kg-sync-lib/coverage/lcov-report/prettify.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    name: W
    signature: W(al)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:f1e13714
    "@type": function
    title: anonymous
    description: Function  in packages/kg-sync-lib/coverage/lcov-report/prettify.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:b1d6d08a
    "@type": function
    title: a
    description: Function a(V) in packages/kg-sync-lib/coverage/lcov-report/prettify.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    name: a
    signature: a(V)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:28f864ac
    "@type": function
    title: aa
    description: Function aa(ab) in packages/kg-sync-lib/coverage/lcov-report/prettify.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    name: aa
    signature: aa(ab)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:92ce3a4c
    "@type": function
    title: B
    description: Function B(S,U,W,T) in
      packages/kg-sync-lib/coverage/lcov-report/prettify.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    name: B
    signature: B(S,U,W,T)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:b9cada14
    "@type": function
    title: o
    description: Function o(S) in packages/kg-sync-lib/coverage/lcov-report/prettify.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    name: o
    signature: o(S)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:bfc3bcce
    "@type": function
    title: g
    description: Function g(U,T) in packages/kg-sync-lib/coverage/lcov-report/prettify.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    name: g
    signature: g(U,T)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:f1e13714
    "@type": function
    title: anonymous
    description: Function  in packages/kg-sync-lib/coverage/lcov-report/prettify.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:f1e13714
    "@type": function
    title: anonymous
    description: Function  in packages/kg-sync-lib/coverage/lcov-report/prettify.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:4cad8dad
    "@type": function
    title: i
    description: Function i(T) in packages/kg-sync-lib/coverage/lcov-report/prettify.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    name: i
    signature: i(T)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:c7f1bf3b
    "@type": function
    title: Q
    description: Function Q(V,ag) in packages/kg-sync-lib/coverage/lcov-report/prettify.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    name: Q
    signature: Q(V,ag)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:3ff61363
    "@type": function
    title: ae
    description: Function ae(al) in packages/kg-sync-lib/coverage/lcov-report/prettify.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    name: ae
    signature: ae(al)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:646b9caa
    "@type": function
    title: ad
    description: Function ad(ak) in packages/kg-sync-lib/coverage/lcov-report/prettify.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    name: ad
    signature: ad(ak)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:2a1e0b88
    "@type": function
    title: ai
    description: Function ai(al,ar) in packages/kg-sync-lib/coverage/lcov-report/prettify.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    name: ai
    signature: ai(al,ar)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:2713f478
    "@type": function
    title: D
    description: Function D(ac) in packages/kg-sync-lib/coverage/lcov-report/prettify.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    name: D
    signature: D(ac)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:67154efa
    "@type": function
    title: c
    description: Function c(U,V) in packages/kg-sync-lib/coverage/lcov-report/prettify.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    name: c
    signature: c(U,V)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:a42457fb
    "@type": function
    title: q
    description: Function q(T,S) in packages/kg-sync-lib/coverage/lcov-report/prettify.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    name: q
    signature: q(T,S)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:5212edee
    "@type": function
    title: d
    description: Function d(V) in packages/kg-sync-lib/coverage/lcov-report/prettify.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    name: d
    signature: d(V)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:b51809a3
    "@type": function
    title: y
    description: Function y(W,V,U) in packages/kg-sync-lib/coverage/lcov-report/prettify.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    name: y
    signature: y(W,V,U)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:4dca96c2
    "@type": function
    title: b
    description: Function b(ad) in packages/kg-sync-lib/coverage/lcov-report/prettify.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    name: b
    signature: b(ad)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:51eec226
    "@type": function
    title: Y
    description: Function Y(af) in packages/kg-sync-lib/coverage/lcov-report/prettify.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    name: Y
    signature: Y(af)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:f1e13714
    "@type": function
    title: anonymous
    description: Function  in packages/kg-sync-lib/coverage/lcov-report/prettify.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:70fe7769
    "@type": function
    title: U
    description: Function U() in packages/kg-sync-lib/coverage/lcov-report/prettify.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    name: U
    signature: U()
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:38f6219b
    "@type": function
    title: anonymous
    description: Function  in packages/kg-sync-lib/coverage/lcov-report/sorter.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 194
  - "@id": function:6cb5abaf
    "@type": function
    title: getTable
    description: Function getTable() in packages/kg-sync-lib/coverage/lcov-report/sorter.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/sorter.js
    name: getTable
    signature: getTable()
    lang: javascript
    line_start: 11
    line_end: 13
  - "@id": function:6b2e20a6
    "@type": function
    title: getTableHeader
    description: Function getTableHeader() in
      packages/kg-sync-lib/coverage/lcov-report/sorter.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/sorter.js
    name: getTableHeader
    signature: getTableHeader()
    lang: javascript
    line_start: 15
    line_end: 17
  - "@id": function:d4d761d8
    "@type": function
    title: getTableBody
    description: Function getTableBody() in
      packages/kg-sync-lib/coverage/lcov-report/sorter.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/sorter.js
    name: getTableBody
    signature: getTableBody()
    lang: javascript
    line_start: 19
    line_end: 21
  - "@id": function:947ab0bf
    "@type": function
    title: getNthColumn
    description: Function getNthColumn(n) in
      packages/kg-sync-lib/coverage/lcov-report/sorter.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/sorter.js
    name: getNthColumn
    signature: getNthColumn(n)
    lang: javascript
    line_start: 23
    line_end: 25
  - "@id": function:2c8a82f9
    "@type": function
    title: onFilterInput
    description: Function onFilterInput() in
      packages/kg-sync-lib/coverage/lcov-report/sorter.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/sorter.js
    name: onFilterInput
    signature: onFilterInput()
    lang: javascript
    line_start: 27
    line_end: 42
  - "@id": function:c5507e9b
    "@type": function
    title: addSearchBox
    description: Function addSearchBox() in
      packages/kg-sync-lib/coverage/lcov-report/sorter.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/sorter.js
    name: addSearchBox
    signature: addSearchBox()
    lang: javascript
    line_start: 45
    line_end: 50
  - "@id": function:5157857e
    "@type": function
    title: loadColumns
    description: Function loadColumns() in
      packages/kg-sync-lib/coverage/lcov-report/sorter.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/sorter.js
    name: loadColumns
    signature: loadColumns()
    lang: javascript
    line_start: 53
    line_end: 75
  - "@id": function:c1912697
    "@type": function
    title: loadRowData
    description: Function loadRowData(tableRow) in
      packages/kg-sync-lib/coverage/lcov-report/sorter.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/sorter.js
    name: loadRowData
    signature: loadRowData(tableRow)
    lang: javascript
    line_start: 78
    line_end: 95
  - "@id": function:aaacd7f9
    "@type": function
    title: loadData
    description: Function loadData() in packages/kg-sync-lib/coverage/lcov-report/sorter.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/sorter.js
    name: loadData
    signature: loadData()
    lang: javascript
    line_start: 97
    line_end: 104
  - "@id": function:1dcce6d2
    "@type": function
    title: sortByIndex
    description: Function sortByIndex(index, desc) in
      packages/kg-sync-lib/coverage/lcov-report/sorter.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/sorter.js
    name: sortByIndex
    signature: sortByIndex(index, desc)
    lang: javascript
    line_start: 106
    line_end: 135
  - "@id": function:b3759c08
    "@type": function
    title: anonymous
    description: Function  in packages/kg-sync-lib/coverage/lcov-report/sorter.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 108
    line_end: 112
  - "@id": function:edf80830
    "@type": function
    title: anonymous
    description: Function  in packages/kg-sync-lib/coverage/lcov-report/sorter.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 120
    line_end: 122
  - "@id": function:9a7dbbd8
    "@type": function
    title: removeSortIndicators
    description: Function removeSortIndicators() in
      packages/kg-sync-lib/coverage/lcov-report/sorter.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/sorter.js
    name: removeSortIndicators
    signature: removeSortIndicators()
    lang: javascript
    line_start: 137
    line_end: 143
  - "@id": function:eed649c4
    "@type": function
    title: addSortIndicators
    description: Function addSortIndicators() in
      packages/kg-sync-lib/coverage/lcov-report/sorter.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/sorter.js
    name: addSortIndicators
    signature: addSortIndicators()
    lang: javascript
    line_start: 145
    line_end: 149
  - "@id": function:b1ba1402
    "@type": function
    title: enableUI
    description: Function enableUI() in packages/kg-sync-lib/coverage/lcov-report/sorter.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/sorter.js
    name: enableUI
    signature: enableUI()
    lang: javascript
    line_start: 151
    line_end: 182
  - "@id": function:6079eea6
    "@type": function
    title: anonymous
    description: Function  in packages/kg-sync-lib/coverage/lcov-report/sorter.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 154
    line_end: 169
  - "@id": function:2b065703
    "@type": function
    title: anonymous
    description: Function  in packages/kg-sync-lib/coverage/lcov-report/sorter.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 157
    line_end: 168
  - "@id": function:8a48ca15
    "@type": function
    title: anonymous
    description: Function  in packages/kg-sync-lib/coverage/lcov-report/sorter.js
    filePath: packages/kg-sync-lib/coverage/lcov-report/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 184
    line_end: 193
  - "@id": function:ca845fc1
    "@type": function
    title: anonymous
    description: Function  in packages/kg-sync-lib/coverage/prettify.js
    filePath: packages/kg-sync-lib/coverage/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:fa841b33
    "@type": function
    title: k
    description: Function k(Z) in packages/kg-sync-lib/coverage/prettify.js
    filePath: packages/kg-sync-lib/coverage/prettify.js
    name: k
    signature: k(Z)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:9780da34
    "@type": function
    title: ab
    description: Function ab(ah) in packages/kg-sync-lib/coverage/prettify.js
    filePath: packages/kg-sync-lib/coverage/prettify.js
    name: ab
    signature: ab(ah)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:06a371e2
    "@type": function
    title: T
    description: Function T(af) in packages/kg-sync-lib/coverage/prettify.js
    filePath: packages/kg-sync-lib/coverage/prettify.js
    name: T
    signature: T(af)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:fc9ff684
    "@type": function
    title: X
    description: Function X(am) in packages/kg-sync-lib/coverage/prettify.js
    filePath: packages/kg-sync-lib/coverage/prettify.js
    name: X
    signature: X(am)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:ca845fc1
    "@type": function
    title: anonymous
    description: Function  in packages/kg-sync-lib/coverage/prettify.js
    filePath: packages/kg-sync-lib/coverage/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:d51ada8f
    "@type": function
    title: W
    description: Function W(al) in packages/kg-sync-lib/coverage/prettify.js
    filePath: packages/kg-sync-lib/coverage/prettify.js
    name: W
    signature: W(al)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:ca845fc1
    "@type": function
    title: anonymous
    description: Function  in packages/kg-sync-lib/coverage/prettify.js
    filePath: packages/kg-sync-lib/coverage/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:f871a74a
    "@type": function
    title: a
    description: Function a(V) in packages/kg-sync-lib/coverage/prettify.js
    filePath: packages/kg-sync-lib/coverage/prettify.js
    name: a
    signature: a(V)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:10e515b9
    "@type": function
    title: aa
    description: Function aa(ab) in packages/kg-sync-lib/coverage/prettify.js
    filePath: packages/kg-sync-lib/coverage/prettify.js
    name: aa
    signature: aa(ab)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:0877d5d9
    "@type": function
    title: B
    description: Function B(S,U,W,T) in packages/kg-sync-lib/coverage/prettify.js
    filePath: packages/kg-sync-lib/coverage/prettify.js
    name: B
    signature: B(S,U,W,T)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:aa162a1b
    "@type": function
    title: o
    description: Function o(S) in packages/kg-sync-lib/coverage/prettify.js
    filePath: packages/kg-sync-lib/coverage/prettify.js
    name: o
    signature: o(S)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:cbfa0c28
    "@type": function
    title: g
    description: Function g(U,T) in packages/kg-sync-lib/coverage/prettify.js
    filePath: packages/kg-sync-lib/coverage/prettify.js
    name: g
    signature: g(U,T)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:ca845fc1
    "@type": function
    title: anonymous
    description: Function  in packages/kg-sync-lib/coverage/prettify.js
    filePath: packages/kg-sync-lib/coverage/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:ca845fc1
    "@type": function
    title: anonymous
    description: Function  in packages/kg-sync-lib/coverage/prettify.js
    filePath: packages/kg-sync-lib/coverage/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:02bc5d55
    "@type": function
    title: i
    description: Function i(T) in packages/kg-sync-lib/coverage/prettify.js
    filePath: packages/kg-sync-lib/coverage/prettify.js
    name: i
    signature: i(T)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:af37fe28
    "@type": function
    title: Q
    description: Function Q(V,ag) in packages/kg-sync-lib/coverage/prettify.js
    filePath: packages/kg-sync-lib/coverage/prettify.js
    name: Q
    signature: Q(V,ag)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:33287b43
    "@type": function
    title: ae
    description: Function ae(al) in packages/kg-sync-lib/coverage/prettify.js
    filePath: packages/kg-sync-lib/coverage/prettify.js
    name: ae
    signature: ae(al)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:efff3355
    "@type": function
    title: ad
    description: Function ad(ak) in packages/kg-sync-lib/coverage/prettify.js
    filePath: packages/kg-sync-lib/coverage/prettify.js
    name: ad
    signature: ad(ak)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:0b41ed0d
    "@type": function
    title: ai
    description: Function ai(al,ar) in packages/kg-sync-lib/coverage/prettify.js
    filePath: packages/kg-sync-lib/coverage/prettify.js
    name: ai
    signature: ai(al,ar)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:8cc7ce3f
    "@type": function
    title: D
    description: Function D(ac) in packages/kg-sync-lib/coverage/prettify.js
    filePath: packages/kg-sync-lib/coverage/prettify.js
    name: D
    signature: D(ac)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:ec069283
    "@type": function
    title: c
    description: Function c(U,V) in packages/kg-sync-lib/coverage/prettify.js
    filePath: packages/kg-sync-lib/coverage/prettify.js
    name: c
    signature: c(U,V)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:6cc98df5
    "@type": function
    title: q
    description: Function q(T,S) in packages/kg-sync-lib/coverage/prettify.js
    filePath: packages/kg-sync-lib/coverage/prettify.js
    name: q
    signature: q(T,S)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:8b5c3393
    "@type": function
    title: d
    description: Function d(V) in packages/kg-sync-lib/coverage/prettify.js
    filePath: packages/kg-sync-lib/coverage/prettify.js
    name: d
    signature: d(V)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:7c6e41a2
    "@type": function
    title: y
    description: Function y(W,V,U) in packages/kg-sync-lib/coverage/prettify.js
    filePath: packages/kg-sync-lib/coverage/prettify.js
    name: y
    signature: y(W,V,U)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:b6bb69eb
    "@type": function
    title: b
    description: Function b(ad) in packages/kg-sync-lib/coverage/prettify.js
    filePath: packages/kg-sync-lib/coverage/prettify.js
    name: b
    signature: b(ad)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:062cfaa4
    "@type": function
    title: Y
    description: Function Y(af) in packages/kg-sync-lib/coverage/prettify.js
    filePath: packages/kg-sync-lib/coverage/prettify.js
    name: Y
    signature: Y(af)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:ca845fc1
    "@type": function
    title: anonymous
    description: Function  in packages/kg-sync-lib/coverage/prettify.js
    filePath: packages/kg-sync-lib/coverage/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:c37b5bfa
    "@type": function
    title: U
    description: Function U() in packages/kg-sync-lib/coverage/prettify.js
    filePath: packages/kg-sync-lib/coverage/prettify.js
    name: U
    signature: U()
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:3f5c6d08
    "@type": function
    title: anonymous
    description: Function  in packages/kg-sync-lib/coverage/sorter.js
    filePath: packages/kg-sync-lib/coverage/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 194
  - "@id": function:26142384
    "@type": function
    title: getTable
    description: Function getTable() in packages/kg-sync-lib/coverage/sorter.js
    filePath: packages/kg-sync-lib/coverage/sorter.js
    name: getTable
    signature: getTable()
    lang: javascript
    line_start: 11
    line_end: 13
  - "@id": function:e216d051
    "@type": function
    title: getTableHeader
    description: Function getTableHeader() in packages/kg-sync-lib/coverage/sorter.js
    filePath: packages/kg-sync-lib/coverage/sorter.js
    name: getTableHeader
    signature: getTableHeader()
    lang: javascript
    line_start: 15
    line_end: 17
  - "@id": function:db627f03
    "@type": function
    title: getTableBody
    description: Function getTableBody() in packages/kg-sync-lib/coverage/sorter.js
    filePath: packages/kg-sync-lib/coverage/sorter.js
    name: getTableBody
    signature: getTableBody()
    lang: javascript
    line_start: 19
    line_end: 21
  - "@id": function:4f65d303
    "@type": function
    title: getNthColumn
    description: Function getNthColumn(n) in packages/kg-sync-lib/coverage/sorter.js
    filePath: packages/kg-sync-lib/coverage/sorter.js
    name: getNthColumn
    signature: getNthColumn(n)
    lang: javascript
    line_start: 23
    line_end: 25
  - "@id": function:1ce49476
    "@type": function
    title: onFilterInput
    description: Function onFilterInput() in packages/kg-sync-lib/coverage/sorter.js
    filePath: packages/kg-sync-lib/coverage/sorter.js
    name: onFilterInput
    signature: onFilterInput()
    lang: javascript
    line_start: 27
    line_end: 42
  - "@id": function:8694ab8d
    "@type": function
    title: addSearchBox
    description: Function addSearchBox() in packages/kg-sync-lib/coverage/sorter.js
    filePath: packages/kg-sync-lib/coverage/sorter.js
    name: addSearchBox
    signature: addSearchBox()
    lang: javascript
    line_start: 45
    line_end: 50
  - "@id": function:1dbb889c
    "@type": function
    title: loadColumns
    description: Function loadColumns() in packages/kg-sync-lib/coverage/sorter.js
    filePath: packages/kg-sync-lib/coverage/sorter.js
    name: loadColumns
    signature: loadColumns()
    lang: javascript
    line_start: 53
    line_end: 75
  - "@id": function:9b3367b6
    "@type": function
    title: loadRowData
    description: Function loadRowData(tableRow) in packages/kg-sync-lib/coverage/sorter.js
    filePath: packages/kg-sync-lib/coverage/sorter.js
    name: loadRowData
    signature: loadRowData(tableRow)
    lang: javascript
    line_start: 78
    line_end: 95
  - "@id": function:4e5e4a1f
    "@type": function
    title: loadData
    description: Function loadData() in packages/kg-sync-lib/coverage/sorter.js
    filePath: packages/kg-sync-lib/coverage/sorter.js
    name: loadData
    signature: loadData()
    lang: javascript
    line_start: 97
    line_end: 104
  - "@id": function:2d37ae79
    "@type": function
    title: sortByIndex
    description: Function sortByIndex(index, desc) in
      packages/kg-sync-lib/coverage/sorter.js
    filePath: packages/kg-sync-lib/coverage/sorter.js
    name: sortByIndex
    signature: sortByIndex(index, desc)
    lang: javascript
    line_start: 106
    line_end: 135
  - "@id": function:a7e8ff7f
    "@type": function
    title: anonymous
    description: Function  in packages/kg-sync-lib/coverage/sorter.js
    filePath: packages/kg-sync-lib/coverage/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 108
    line_end: 112
  - "@id": function:41c1ce56
    "@type": function
    title: anonymous
    description: Function  in packages/kg-sync-lib/coverage/sorter.js
    filePath: packages/kg-sync-lib/coverage/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 120
    line_end: 122
  - "@id": function:21abb80f
    "@type": function
    title: removeSortIndicators
    description: Function removeSortIndicators() in packages/kg-sync-lib/coverage/sorter.js
    filePath: packages/kg-sync-lib/coverage/sorter.js
    name: removeSortIndicators
    signature: removeSortIndicators()
    lang: javascript
    line_start: 137
    line_end: 143
  - "@id": function:e7ac8d20
    "@type": function
    title: addSortIndicators
    description: Function addSortIndicators() in packages/kg-sync-lib/coverage/sorter.js
    filePath: packages/kg-sync-lib/coverage/sorter.js
    name: addSortIndicators
    signature: addSortIndicators()
    lang: javascript
    line_start: 145
    line_end: 149
  - "@id": function:5c362229
    "@type": function
    title: enableUI
    description: Function enableUI() in packages/kg-sync-lib/coverage/sorter.js
    filePath: packages/kg-sync-lib/coverage/sorter.js
    name: enableUI
    signature: enableUI()
    lang: javascript
    line_start: 151
    line_end: 182
  - "@id": function:bc09a2d6
    "@type": function
    title: anonymous
    description: Function  in packages/kg-sync-lib/coverage/sorter.js
    filePath: packages/kg-sync-lib/coverage/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 154
    line_end: 169
  - "@id": function:3767cf56
    "@type": function
    title: anonymous
    description: Function  in packages/kg-sync-lib/coverage/sorter.js
    filePath: packages/kg-sync-lib/coverage/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 157
    line_end: 168
  - "@id": function:27a16bce
    "@type": function
    title: anonymous
    description: Function  in packages/kg-sync-lib/coverage/sorter.js
    filePath: packages/kg-sync-lib/coverage/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 184
    line_end: 193
  - "@id": function:08d867c4
    "@type": function
    title: anonymous
    description: Function  in packages/shared/coverage/block-navigation.js
    filePath: packages/shared/coverage/block-navigation.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 86
  - "@id": function:81dc8786
    "@type": function
    title: toggleClass
    description: Function toggleClass(index) in packages/shared/coverage/block-navigation.js
    filePath: packages/shared/coverage/block-navigation.js
    name: toggleClass
    signature: toggleClass(index)
    lang: javascript
    line_start: 24
    line_end: 29
  - "@id": function:cee40c1a
    "@type": function
    title: makeCurrent
    description: Function makeCurrent(index) in packages/shared/coverage/block-navigation.js
    filePath: packages/shared/coverage/block-navigation.js
    name: makeCurrent
    signature: makeCurrent(index)
    lang: javascript
    line_start: 31
    line_end: 39
  - "@id": function:e20d15fe
    "@type": function
    title: goToPrevious
    description: Function goToPrevious() in packages/shared/coverage/block-navigation.js
    filePath: packages/shared/coverage/block-navigation.js
    name: goToPrevious
    signature: goToPrevious()
    lang: javascript
    line_start: 41
    line_end: 50
  - "@id": function:2f64c69c
    "@type": function
    title: goToNext
    description: Function goToNext() in packages/shared/coverage/block-navigation.js
    filePath: packages/shared/coverage/block-navigation.js
    name: goToNext
    signature: goToNext()
    lang: javascript
    line_start: 52
    line_end: 63
  - "@id": function:acd5437a
    "@type": function
    title: anonymous
    description: Function  in packages/shared/coverage/block-navigation.js
    filePath: packages/shared/coverage/block-navigation.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 65
    line_end: 85
  - "@id": function:bbbf3cca
    "@type": function
    title: anonymous
    description: Function  in packages/shared/coverage/lcov-report/block-navigation.js
    filePath: packages/shared/coverage/lcov-report/block-navigation.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 86
  - "@id": function:93530fdd
    "@type": function
    title: toggleClass
    description: Function toggleClass(index) in
      packages/shared/coverage/lcov-report/block-navigation.js
    filePath: packages/shared/coverage/lcov-report/block-navigation.js
    name: toggleClass
    signature: toggleClass(index)
    lang: javascript
    line_start: 24
    line_end: 29
  - "@id": function:4c1e1bc8
    "@type": function
    title: makeCurrent
    description: Function makeCurrent(index) in
      packages/shared/coverage/lcov-report/block-navigation.js
    filePath: packages/shared/coverage/lcov-report/block-navigation.js
    name: makeCurrent
    signature: makeCurrent(index)
    lang: javascript
    line_start: 31
    line_end: 39
  - "@id": function:a6eb4585
    "@type": function
    title: goToPrevious
    description: Function goToPrevious() in
      packages/shared/coverage/lcov-report/block-navigation.js
    filePath: packages/shared/coverage/lcov-report/block-navigation.js
    name: goToPrevious
    signature: goToPrevious()
    lang: javascript
    line_start: 41
    line_end: 50
  - "@id": function:6d68fe53
    "@type": function
    title: goToNext
    description: Function goToNext() in
      packages/shared/coverage/lcov-report/block-navigation.js
    filePath: packages/shared/coverage/lcov-report/block-navigation.js
    name: goToNext
    signature: goToNext()
    lang: javascript
    line_start: 52
    line_end: 63
  - "@id": function:7c13dabe
    "@type": function
    title: anonymous
    description: Function  in packages/shared/coverage/lcov-report/block-navigation.js
    filePath: packages/shared/coverage/lcov-report/block-navigation.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 65
    line_end: 85
  - "@id": function:8fee06e7
    "@type": function
    title: anonymous
    description: Function  in packages/shared/coverage/lcov-report/prettify.js
    filePath: packages/shared/coverage/lcov-report/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:c68242cf
    "@type": function
    title: k
    description: Function k(Z) in packages/shared/coverage/lcov-report/prettify.js
    filePath: packages/shared/coverage/lcov-report/prettify.js
    name: k
    signature: k(Z)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:c0c03c5a
    "@type": function
    title: ab
    description: Function ab(ah) in packages/shared/coverage/lcov-report/prettify.js
    filePath: packages/shared/coverage/lcov-report/prettify.js
    name: ab
    signature: ab(ah)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:ae6cbeba
    "@type": function
    title: T
    description: Function T(af) in packages/shared/coverage/lcov-report/prettify.js
    filePath: packages/shared/coverage/lcov-report/prettify.js
    name: T
    signature: T(af)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:3c3b0983
    "@type": function
    title: X
    description: Function X(am) in packages/shared/coverage/lcov-report/prettify.js
    filePath: packages/shared/coverage/lcov-report/prettify.js
    name: X
    signature: X(am)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:8fee06e7
    "@type": function
    title: anonymous
    description: Function  in packages/shared/coverage/lcov-report/prettify.js
    filePath: packages/shared/coverage/lcov-report/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:b2bcc7d4
    "@type": function
    title: W
    description: Function W(al) in packages/shared/coverage/lcov-report/prettify.js
    filePath: packages/shared/coverage/lcov-report/prettify.js
    name: W
    signature: W(al)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:8fee06e7
    "@type": function
    title: anonymous
    description: Function  in packages/shared/coverage/lcov-report/prettify.js
    filePath: packages/shared/coverage/lcov-report/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:6161b682
    "@type": function
    title: a
    description: Function a(V) in packages/shared/coverage/lcov-report/prettify.js
    filePath: packages/shared/coverage/lcov-report/prettify.js
    name: a
    signature: a(V)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:23b3ef83
    "@type": function
    title: aa
    description: Function aa(ab) in packages/shared/coverage/lcov-report/prettify.js
    filePath: packages/shared/coverage/lcov-report/prettify.js
    name: aa
    signature: aa(ab)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:71669fa8
    "@type": function
    title: B
    description: Function B(S,U,W,T) in packages/shared/coverage/lcov-report/prettify.js
    filePath: packages/shared/coverage/lcov-report/prettify.js
    name: B
    signature: B(S,U,W,T)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:40c3ad3e
    "@type": function
    title: o
    description: Function o(S) in packages/shared/coverage/lcov-report/prettify.js
    filePath: packages/shared/coverage/lcov-report/prettify.js
    name: o
    signature: o(S)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:53254329
    "@type": function
    title: g
    description: Function g(U,T) in packages/shared/coverage/lcov-report/prettify.js
    filePath: packages/shared/coverage/lcov-report/prettify.js
    name: g
    signature: g(U,T)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:8fee06e7
    "@type": function
    title: anonymous
    description: Function  in packages/shared/coverage/lcov-report/prettify.js
    filePath: packages/shared/coverage/lcov-report/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:8fee06e7
    "@type": function
    title: anonymous
    description: Function  in packages/shared/coverage/lcov-report/prettify.js
    filePath: packages/shared/coverage/lcov-report/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:31cd6fc5
    "@type": function
    title: i
    description: Function i(T) in packages/shared/coverage/lcov-report/prettify.js
    filePath: packages/shared/coverage/lcov-report/prettify.js
    name: i
    signature: i(T)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:47060299
    "@type": function
    title: Q
    description: Function Q(V,ag) in packages/shared/coverage/lcov-report/prettify.js
    filePath: packages/shared/coverage/lcov-report/prettify.js
    name: Q
    signature: Q(V,ag)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:4ae2280a
    "@type": function
    title: ae
    description: Function ae(al) in packages/shared/coverage/lcov-report/prettify.js
    filePath: packages/shared/coverage/lcov-report/prettify.js
    name: ae
    signature: ae(al)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:85523f16
    "@type": function
    title: ad
    description: Function ad(ak) in packages/shared/coverage/lcov-report/prettify.js
    filePath: packages/shared/coverage/lcov-report/prettify.js
    name: ad
    signature: ad(ak)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:ba52438d
    "@type": function
    title: ai
    description: Function ai(al,ar) in packages/shared/coverage/lcov-report/prettify.js
    filePath: packages/shared/coverage/lcov-report/prettify.js
    name: ai
    signature: ai(al,ar)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:ea948fbf
    "@type": function
    title: D
    description: Function D(ac) in packages/shared/coverage/lcov-report/prettify.js
    filePath: packages/shared/coverage/lcov-report/prettify.js
    name: D
    signature: D(ac)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:3440670f
    "@type": function
    title: c
    description: Function c(U,V) in packages/shared/coverage/lcov-report/prettify.js
    filePath: packages/shared/coverage/lcov-report/prettify.js
    name: c
    signature: c(U,V)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:6df15a39
    "@type": function
    title: q
    description: Function q(T,S) in packages/shared/coverage/lcov-report/prettify.js
    filePath: packages/shared/coverage/lcov-report/prettify.js
    name: q
    signature: q(T,S)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:b1c2b939
    "@type": function
    title: d
    description: Function d(V) in packages/shared/coverage/lcov-report/prettify.js
    filePath: packages/shared/coverage/lcov-report/prettify.js
    name: d
    signature: d(V)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:65751024
    "@type": function
    title: y
    description: Function y(W,V,U) in packages/shared/coverage/lcov-report/prettify.js
    filePath: packages/shared/coverage/lcov-report/prettify.js
    name: y
    signature: y(W,V,U)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:daec0e87
    "@type": function
    title: b
    description: Function b(ad) in packages/shared/coverage/lcov-report/prettify.js
    filePath: packages/shared/coverage/lcov-report/prettify.js
    name: b
    signature: b(ad)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:765b04bd
    "@type": function
    title: Y
    description: Function Y(af) in packages/shared/coverage/lcov-report/prettify.js
    filePath: packages/shared/coverage/lcov-report/prettify.js
    name: Y
    signature: Y(af)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:8fee06e7
    "@type": function
    title: anonymous
    description: Function  in packages/shared/coverage/lcov-report/prettify.js
    filePath: packages/shared/coverage/lcov-report/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:6f16fc06
    "@type": function
    title: U
    description: Function U() in packages/shared/coverage/lcov-report/prettify.js
    filePath: packages/shared/coverage/lcov-report/prettify.js
    name: U
    signature: U()
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:c036e449
    "@type": function
    title: anonymous
    description: Function  in packages/shared/coverage/lcov-report/sorter.js
    filePath: packages/shared/coverage/lcov-report/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 194
  - "@id": function:a5e63525
    "@type": function
    title: getTable
    description: Function getTable() in packages/shared/coverage/lcov-report/sorter.js
    filePath: packages/shared/coverage/lcov-report/sorter.js
    name: getTable
    signature: getTable()
    lang: javascript
    line_start: 11
    line_end: 13
  - "@id": function:a3593337
    "@type": function
    title: getTableHeader
    description: Function getTableHeader() in packages/shared/coverage/lcov-report/sorter.js
    filePath: packages/shared/coverage/lcov-report/sorter.js
    name: getTableHeader
    signature: getTableHeader()
    lang: javascript
    line_start: 15
    line_end: 17
  - "@id": function:e91e356d
    "@type": function
    title: getTableBody
    description: Function getTableBody() in packages/shared/coverage/lcov-report/sorter.js
    filePath: packages/shared/coverage/lcov-report/sorter.js
    name: getTableBody
    signature: getTableBody()
    lang: javascript
    line_start: 19
    line_end: 21
  - "@id": function:dc030dfc
    "@type": function
    title: getNthColumn
    description: Function getNthColumn(n) in packages/shared/coverage/lcov-report/sorter.js
    filePath: packages/shared/coverage/lcov-report/sorter.js
    name: getNthColumn
    signature: getNthColumn(n)
    lang: javascript
    line_start: 23
    line_end: 25
  - "@id": function:8dfc2bde
    "@type": function
    title: onFilterInput
    description: Function onFilterInput() in packages/shared/coverage/lcov-report/sorter.js
    filePath: packages/shared/coverage/lcov-report/sorter.js
    name: onFilterInput
    signature: onFilterInput()
    lang: javascript
    line_start: 27
    line_end: 42
  - "@id": function:e00e9013
    "@type": function
    title: addSearchBox
    description: Function addSearchBox() in packages/shared/coverage/lcov-report/sorter.js
    filePath: packages/shared/coverage/lcov-report/sorter.js
    name: addSearchBox
    signature: addSearchBox()
    lang: javascript
    line_start: 45
    line_end: 50
  - "@id": function:6ea423a8
    "@type": function
    title: loadColumns
    description: Function loadColumns() in packages/shared/coverage/lcov-report/sorter.js
    filePath: packages/shared/coverage/lcov-report/sorter.js
    name: loadColumns
    signature: loadColumns()
    lang: javascript
    line_start: 53
    line_end: 75
  - "@id": function:06cd648c
    "@type": function
    title: loadRowData
    description: Function loadRowData(tableRow) in
      packages/shared/coverage/lcov-report/sorter.js
    filePath: packages/shared/coverage/lcov-report/sorter.js
    name: loadRowData
    signature: loadRowData(tableRow)
    lang: javascript
    line_start: 78
    line_end: 95
  - "@id": function:b52ed5d0
    "@type": function
    title: loadData
    description: Function loadData() in packages/shared/coverage/lcov-report/sorter.js
    filePath: packages/shared/coverage/lcov-report/sorter.js
    name: loadData
    signature: loadData()
    lang: javascript
    line_start: 97
    line_end: 104
  - "@id": function:38a45f42
    "@type": function
    title: sortByIndex
    description: Function sortByIndex(index, desc) in
      packages/shared/coverage/lcov-report/sorter.js
    filePath: packages/shared/coverage/lcov-report/sorter.js
    name: sortByIndex
    signature: sortByIndex(index, desc)
    lang: javascript
    line_start: 106
    line_end: 135
  - "@id": function:d3bf0c24
    "@type": function
    title: anonymous
    description: Function  in packages/shared/coverage/lcov-report/sorter.js
    filePath: packages/shared/coverage/lcov-report/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 108
    line_end: 112
  - "@id": function:1b6162cf
    "@type": function
    title: anonymous
    description: Function  in packages/shared/coverage/lcov-report/sorter.js
    filePath: packages/shared/coverage/lcov-report/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 120
    line_end: 122
  - "@id": function:870b9184
    "@type": function
    title: removeSortIndicators
    description: Function removeSortIndicators() in
      packages/shared/coverage/lcov-report/sorter.js
    filePath: packages/shared/coverage/lcov-report/sorter.js
    name: removeSortIndicators
    signature: removeSortIndicators()
    lang: javascript
    line_start: 137
    line_end: 143
  - "@id": function:cf770f1b
    "@type": function
    title: addSortIndicators
    description: Function addSortIndicators() in
      packages/shared/coverage/lcov-report/sorter.js
    filePath: packages/shared/coverage/lcov-report/sorter.js
    name: addSortIndicators
    signature: addSortIndicators()
    lang: javascript
    line_start: 145
    line_end: 149
  - "@id": function:8f837732
    "@type": function
    title: enableUI
    description: Function enableUI() in packages/shared/coverage/lcov-report/sorter.js
    filePath: packages/shared/coverage/lcov-report/sorter.js
    name: enableUI
    signature: enableUI()
    lang: javascript
    line_start: 151
    line_end: 182
  - "@id": function:c50e5973
    "@type": function
    title: anonymous
    description: Function  in packages/shared/coverage/lcov-report/sorter.js
    filePath: packages/shared/coverage/lcov-report/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 154
    line_end: 169
  - "@id": function:8d599df2
    "@type": function
    title: anonymous
    description: Function  in packages/shared/coverage/lcov-report/sorter.js
    filePath: packages/shared/coverage/lcov-report/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 157
    line_end: 168
  - "@id": function:1651c21c
    "@type": function
    title: anonymous
    description: Function  in packages/shared/coverage/lcov-report/sorter.js
    filePath: packages/shared/coverage/lcov-report/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 184
    line_end: 193
  - "@id": function:b05a869a
    "@type": function
    title: anonymous
    description: Function  in packages/shared/coverage/prettify.js
    filePath: packages/shared/coverage/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:22c27bd3
    "@type": function
    title: k
    description: Function k(Z) in packages/shared/coverage/prettify.js
    filePath: packages/shared/coverage/prettify.js
    name: k
    signature: k(Z)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:06efc2ce
    "@type": function
    title: ab
    description: Function ab(ah) in packages/shared/coverage/prettify.js
    filePath: packages/shared/coverage/prettify.js
    name: ab
    signature: ab(ah)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:e641795c
    "@type": function
    title: T
    description: Function T(af) in packages/shared/coverage/prettify.js
    filePath: packages/shared/coverage/prettify.js
    name: T
    signature: T(af)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:9a4ad8d2
    "@type": function
    title: X
    description: Function X(am) in packages/shared/coverage/prettify.js
    filePath: packages/shared/coverage/prettify.js
    name: X
    signature: X(am)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:b05a869a
    "@type": function
    title: anonymous
    description: Function  in packages/shared/coverage/prettify.js
    filePath: packages/shared/coverage/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:44e969ab
    "@type": function
    title: W
    description: Function W(al) in packages/shared/coverage/prettify.js
    filePath: packages/shared/coverage/prettify.js
    name: W
    signature: W(al)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:b05a869a
    "@type": function
    title: anonymous
    description: Function  in packages/shared/coverage/prettify.js
    filePath: packages/shared/coverage/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:dd8a57d8
    "@type": function
    title: a
    description: Function a(V) in packages/shared/coverage/prettify.js
    filePath: packages/shared/coverage/prettify.js
    name: a
    signature: a(V)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:978ed439
    "@type": function
    title: aa
    description: Function aa(ab) in packages/shared/coverage/prettify.js
    filePath: packages/shared/coverage/prettify.js
    name: aa
    signature: aa(ab)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:04f0a48a
    "@type": function
    title: B
    description: Function B(S,U,W,T) in packages/shared/coverage/prettify.js
    filePath: packages/shared/coverage/prettify.js
    name: B
    signature: B(S,U,W,T)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:b8856c43
    "@type": function
    title: o
    description: Function o(S) in packages/shared/coverage/prettify.js
    filePath: packages/shared/coverage/prettify.js
    name: o
    signature: o(S)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:ca14ff29
    "@type": function
    title: g
    description: Function g(U,T) in packages/shared/coverage/prettify.js
    filePath: packages/shared/coverage/prettify.js
    name: g
    signature: g(U,T)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:b05a869a
    "@type": function
    title: anonymous
    description: Function  in packages/shared/coverage/prettify.js
    filePath: packages/shared/coverage/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:b05a869a
    "@type": function
    title: anonymous
    description: Function  in packages/shared/coverage/prettify.js
    filePath: packages/shared/coverage/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:00ceff5c
    "@type": function
    title: i
    description: Function i(T) in packages/shared/coverage/prettify.js
    filePath: packages/shared/coverage/prettify.js
    name: i
    signature: i(T)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:23f085ab
    "@type": function
    title: Q
    description: Function Q(V,ag) in packages/shared/coverage/prettify.js
    filePath: packages/shared/coverage/prettify.js
    name: Q
    signature: Q(V,ag)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:9c6676c3
    "@type": function
    title: ae
    description: Function ae(al) in packages/shared/coverage/prettify.js
    filePath: packages/shared/coverage/prettify.js
    name: ae
    signature: ae(al)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:09af3ccb
    "@type": function
    title: ad
    description: Function ad(ak) in packages/shared/coverage/prettify.js
    filePath: packages/shared/coverage/prettify.js
    name: ad
    signature: ad(ak)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:44b2e4d7
    "@type": function
    title: ai
    description: Function ai(al,ar) in packages/shared/coverage/prettify.js
    filePath: packages/shared/coverage/prettify.js
    name: ai
    signature: ai(al,ar)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:b016ffd3
    "@type": function
    title: D
    description: Function D(ac) in packages/shared/coverage/prettify.js
    filePath: packages/shared/coverage/prettify.js
    name: D
    signature: D(ac)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:d5d1625b
    "@type": function
    title: c
    description: Function c(U,V) in packages/shared/coverage/prettify.js
    filePath: packages/shared/coverage/prettify.js
    name: c
    signature: c(U,V)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:870b7a0a
    "@type": function
    title: q
    description: Function q(T,S) in packages/shared/coverage/prettify.js
    filePath: packages/shared/coverage/prettify.js
    name: q
    signature: q(T,S)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:8487054c
    "@type": function
    title: d
    description: Function d(V) in packages/shared/coverage/prettify.js
    filePath: packages/shared/coverage/prettify.js
    name: d
    signature: d(V)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:2c9ab12c
    "@type": function
    title: y
    description: Function y(W,V,U) in packages/shared/coverage/prettify.js
    filePath: packages/shared/coverage/prettify.js
    name: y
    signature: y(W,V,U)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:207df40e
    "@type": function
    title: b
    description: Function b(ad) in packages/shared/coverage/prettify.js
    filePath: packages/shared/coverage/prettify.js
    name: b
    signature: b(ad)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:77c9804a
    "@type": function
    title: Y
    description: Function Y(af) in packages/shared/coverage/prettify.js
    filePath: packages/shared/coverage/prettify.js
    name: Y
    signature: Y(af)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:b05a869a
    "@type": function
    title: anonymous
    description: Function  in packages/shared/coverage/prettify.js
    filePath: packages/shared/coverage/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:5f63fed1
    "@type": function
    title: U
    description: Function U() in packages/shared/coverage/prettify.js
    filePath: packages/shared/coverage/prettify.js
    name: U
    signature: U()
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:6f1c5b46
    "@type": function
    title: anonymous
    description: Function  in packages/shared/coverage/sorter.js
    filePath: packages/shared/coverage/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 194
  - "@id": function:2927f223
    "@type": function
    title: getTable
    description: Function getTable() in packages/shared/coverage/sorter.js
    filePath: packages/shared/coverage/sorter.js
    name: getTable
    signature: getTable()
    lang: javascript
    line_start: 11
    line_end: 13
  - "@id": function:31b63fdf
    "@type": function
    title: getTableHeader
    description: Function getTableHeader() in packages/shared/coverage/sorter.js
    filePath: packages/shared/coverage/sorter.js
    name: getTableHeader
    signature: getTableHeader()
    lang: javascript
    line_start: 15
    line_end: 17
  - "@id": function:5a956a6c
    "@type": function
    title: getTableBody
    description: Function getTableBody() in packages/shared/coverage/sorter.js
    filePath: packages/shared/coverage/sorter.js
    name: getTableBody
    signature: getTableBody()
    lang: javascript
    line_start: 19
    line_end: 21
  - "@id": function:9c81fed6
    "@type": function
    title: getNthColumn
    description: Function getNthColumn(n) in packages/shared/coverage/sorter.js
    filePath: packages/shared/coverage/sorter.js
    name: getNthColumn
    signature: getNthColumn(n)
    lang: javascript
    line_start: 23
    line_end: 25
  - "@id": function:a046318a
    "@type": function
    title: onFilterInput
    description: Function onFilterInput() in packages/shared/coverage/sorter.js
    filePath: packages/shared/coverage/sorter.js
    name: onFilterInput
    signature: onFilterInput()
    lang: javascript
    line_start: 27
    line_end: 42
  - "@id": function:56ad7396
    "@type": function
    title: addSearchBox
    description: Function addSearchBox() in packages/shared/coverage/sorter.js
    filePath: packages/shared/coverage/sorter.js
    name: addSearchBox
    signature: addSearchBox()
    lang: javascript
    line_start: 45
    line_end: 50
  - "@id": function:095fecfa
    "@type": function
    title: loadColumns
    description: Function loadColumns() in packages/shared/coverage/sorter.js
    filePath: packages/shared/coverage/sorter.js
    name: loadColumns
    signature: loadColumns()
    lang: javascript
    line_start: 53
    line_end: 75
  - "@id": function:c5dbc766
    "@type": function
    title: loadRowData
    description: Function loadRowData(tableRow) in packages/shared/coverage/sorter.js
    filePath: packages/shared/coverage/sorter.js
    name: loadRowData
    signature: loadRowData(tableRow)
    lang: javascript
    line_start: 78
    line_end: 95
  - "@id": function:d0197fa0
    "@type": function
    title: loadData
    description: Function loadData() in packages/shared/coverage/sorter.js
    filePath: packages/shared/coverage/sorter.js
    name: loadData
    signature: loadData()
    lang: javascript
    line_start: 97
    line_end: 104
  - "@id": function:ca012081
    "@type": function
    title: sortByIndex
    description: Function sortByIndex(index, desc) in packages/shared/coverage/sorter.js
    filePath: packages/shared/coverage/sorter.js
    name: sortByIndex
    signature: sortByIndex(index, desc)
    lang: javascript
    line_start: 106
    line_end: 135
  - "@id": function:7edfcfac
    "@type": function
    title: anonymous
    description: Function  in packages/shared/coverage/sorter.js
    filePath: packages/shared/coverage/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 108
    line_end: 112
  - "@id": function:0dd6b196
    "@type": function
    title: anonymous
    description: Function  in packages/shared/coverage/sorter.js
    filePath: packages/shared/coverage/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 120
    line_end: 122
  - "@id": function:41c09702
    "@type": function
    title: removeSortIndicators
    description: Function removeSortIndicators() in packages/shared/coverage/sorter.js
    filePath: packages/shared/coverage/sorter.js
    name: removeSortIndicators
    signature: removeSortIndicators()
    lang: javascript
    line_start: 137
    line_end: 143
  - "@id": function:3bdec215
    "@type": function
    title: addSortIndicators
    description: Function addSortIndicators() in packages/shared/coverage/sorter.js
    filePath: packages/shared/coverage/sorter.js
    name: addSortIndicators
    signature: addSortIndicators()
    lang: javascript
    line_start: 145
    line_end: 149
  - "@id": function:c16f4d38
    "@type": function
    title: enableUI
    description: Function enableUI() in packages/shared/coverage/sorter.js
    filePath: packages/shared/coverage/sorter.js
    name: enableUI
    signature: enableUI()
    lang: javascript
    line_start: 151
    line_end: 182
  - "@id": function:b907df75
    "@type": function
    title: anonymous
    description: Function  in packages/shared/coverage/sorter.js
    filePath: packages/shared/coverage/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 154
    line_end: 169
  - "@id": function:46f0982d
    "@type": function
    title: anonymous
    description: Function  in packages/shared/coverage/sorter.js
    filePath: packages/shared/coverage/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 157
    line_end: 168
  - "@id": function:9f7ca5b8
    "@type": function
    title: anonymous
    description: Function  in packages/shared/coverage/sorter.js
    filePath: packages/shared/coverage/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 184
    line_end: 193
  - "@id": function:e3148f75
    "@type": function
    title: anonymous
    description: Function  in packages/spec-parser-lib/coverage/block-navigation.js
    filePath: packages/spec-parser-lib/coverage/block-navigation.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 86
  - "@id": function:9daaf9b0
    "@type": function
    title: toggleClass
    description: Function toggleClass(index) in
      packages/spec-parser-lib/coverage/block-navigation.js
    filePath: packages/spec-parser-lib/coverage/block-navigation.js
    name: toggleClass
    signature: toggleClass(index)
    lang: javascript
    line_start: 24
    line_end: 29
  - "@id": function:b03003b9
    "@type": function
    title: makeCurrent
    description: Function makeCurrent(index) in
      packages/spec-parser-lib/coverage/block-navigation.js
    filePath: packages/spec-parser-lib/coverage/block-navigation.js
    name: makeCurrent
    signature: makeCurrent(index)
    lang: javascript
    line_start: 31
    line_end: 39
  - "@id": function:9952d843
    "@type": function
    title: goToPrevious
    description: Function goToPrevious() in
      packages/spec-parser-lib/coverage/block-navigation.js
    filePath: packages/spec-parser-lib/coverage/block-navigation.js
    name: goToPrevious
    signature: goToPrevious()
    lang: javascript
    line_start: 41
    line_end: 50
  - "@id": function:a84805a0
    "@type": function
    title: goToNext
    description: Function goToNext() in
      packages/spec-parser-lib/coverage/block-navigation.js
    filePath: packages/spec-parser-lib/coverage/block-navigation.js
    name: goToNext
    signature: goToNext()
    lang: javascript
    line_start: 52
    line_end: 63
  - "@id": function:97470481
    "@type": function
    title: anonymous
    description: Function  in packages/spec-parser-lib/coverage/block-navigation.js
    filePath: packages/spec-parser-lib/coverage/block-navigation.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 65
    line_end: 85
  - "@id": function:1b61c8d6
    "@type": function
    title: anonymous
    description: Function  in
      packages/spec-parser-lib/coverage/lcov-report/block-navigation.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/block-navigation.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 86
  - "@id": function:108d55ad
    "@type": function
    title: toggleClass
    description: Function toggleClass(index) in
      packages/spec-parser-lib/coverage/lcov-report/block-navigation.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/block-navigation.js
    name: toggleClass
    signature: toggleClass(index)
    lang: javascript
    line_start: 24
    line_end: 29
  - "@id": function:be0a91f1
    "@type": function
    title: makeCurrent
    description: Function makeCurrent(index) in
      packages/spec-parser-lib/coverage/lcov-report/block-navigation.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/block-navigation.js
    name: makeCurrent
    signature: makeCurrent(index)
    lang: javascript
    line_start: 31
    line_end: 39
  - "@id": function:9adda11b
    "@type": function
    title: goToPrevious
    description: Function goToPrevious() in
      packages/spec-parser-lib/coverage/lcov-report/block-navigation.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/block-navigation.js
    name: goToPrevious
    signature: goToPrevious()
    lang: javascript
    line_start: 41
    line_end: 50
  - "@id": function:0be8436d
    "@type": function
    title: goToNext
    description: Function goToNext() in
      packages/spec-parser-lib/coverage/lcov-report/block-navigation.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/block-navigation.js
    name: goToNext
    signature: goToNext()
    lang: javascript
    line_start: 52
    line_end: 63
  - "@id": function:b71dbfa4
    "@type": function
    title: anonymous
    description: Function  in
      packages/spec-parser-lib/coverage/lcov-report/block-navigation.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/block-navigation.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 65
    line_end: 85
  - "@id": function:f73c9d8c
    "@type": function
    title: anonymous
    description: Function  in packages/spec-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:28028026
    "@type": function
    title: k
    description: Function k(Z) in packages/spec-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    name: k
    signature: k(Z)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:8589b1ac
    "@type": function
    title: ab
    description: Function ab(ah) in
      packages/spec-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    name: ab
    signature: ab(ah)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:f9f64d21
    "@type": function
    title: T
    description: Function T(af) in packages/spec-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    name: T
    signature: T(af)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:1448d077
    "@type": function
    title: X
    description: Function X(am) in packages/spec-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    name: X
    signature: X(am)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:f73c9d8c
    "@type": function
    title: anonymous
    description: Function  in packages/spec-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:11494c53
    "@type": function
    title: W
    description: Function W(al) in packages/spec-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    name: W
    signature: W(al)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:f73c9d8c
    "@type": function
    title: anonymous
    description: Function  in packages/spec-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:e0b6bb52
    "@type": function
    title: a
    description: Function a(V) in packages/spec-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    name: a
    signature: a(V)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:3e72bfd7
    "@type": function
    title: aa
    description: Function aa(ab) in
      packages/spec-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    name: aa
    signature: aa(ab)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:4c7b6caf
    "@type": function
    title: B
    description: Function B(S,U,W,T) in
      packages/spec-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    name: B
    signature: B(S,U,W,T)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:09ec2eca
    "@type": function
    title: o
    description: Function o(S) in packages/spec-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    name: o
    signature: o(S)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:cf1947ed
    "@type": function
    title: g
    description: Function g(U,T) in
      packages/spec-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    name: g
    signature: g(U,T)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:f73c9d8c
    "@type": function
    title: anonymous
    description: Function  in packages/spec-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:f73c9d8c
    "@type": function
    title: anonymous
    description: Function  in packages/spec-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:a70179af
    "@type": function
    title: i
    description: Function i(T) in packages/spec-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    name: i
    signature: i(T)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:978ebd1b
    "@type": function
    title: Q
    description: Function Q(V,ag) in
      packages/spec-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    name: Q
    signature: Q(V,ag)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:e97d8ed6
    "@type": function
    title: ae
    description: Function ae(al) in
      packages/spec-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    name: ae
    signature: ae(al)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:0eb7c907
    "@type": function
    title: ad
    description: Function ad(ak) in
      packages/spec-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    name: ad
    signature: ad(ak)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:d3f6320a
    "@type": function
    title: ai
    description: Function ai(al,ar) in
      packages/spec-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    name: ai
    signature: ai(al,ar)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:452cbe79
    "@type": function
    title: D
    description: Function D(ac) in packages/spec-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    name: D
    signature: D(ac)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:d4df7d9f
    "@type": function
    title: c
    description: Function c(U,V) in
      packages/spec-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    name: c
    signature: c(U,V)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:0834be98
    "@type": function
    title: q
    description: Function q(T,S) in
      packages/spec-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    name: q
    signature: q(T,S)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:90208730
    "@type": function
    title: d
    description: Function d(V) in packages/spec-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    name: d
    signature: d(V)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:2a4539b0
    "@type": function
    title: y
    description: Function y(W,V,U) in
      packages/spec-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    name: y
    signature: y(W,V,U)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:30582501
    "@type": function
    title: b
    description: Function b(ad) in packages/spec-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    name: b
    signature: b(ad)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:40064b57
    "@type": function
    title: Y
    description: Function Y(af) in packages/spec-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    name: Y
    signature: Y(af)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:f73c9d8c
    "@type": function
    title: anonymous
    description: Function  in packages/spec-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:6bbaa78e
    "@type": function
    title: U
    description: Function U() in packages/spec-parser-lib/coverage/lcov-report/prettify.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    name: U
    signature: U()
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:e1bfa01c
    "@type": function
    title: anonymous
    description: Function  in packages/spec-parser-lib/coverage/lcov-report/sorter.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 194
  - "@id": function:49eebae2
    "@type": function
    title: getTable
    description: Function getTable() in
      packages/spec-parser-lib/coverage/lcov-report/sorter.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/sorter.js
    name: getTable
    signature: getTable()
    lang: javascript
    line_start: 11
    line_end: 13
  - "@id": function:0666bdb0
    "@type": function
    title: getTableHeader
    description: Function getTableHeader() in
      packages/spec-parser-lib/coverage/lcov-report/sorter.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/sorter.js
    name: getTableHeader
    signature: getTableHeader()
    lang: javascript
    line_start: 15
    line_end: 17
  - "@id": function:8cfe4e37
    "@type": function
    title: getTableBody
    description: Function getTableBody() in
      packages/spec-parser-lib/coverage/lcov-report/sorter.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/sorter.js
    name: getTableBody
    signature: getTableBody()
    lang: javascript
    line_start: 19
    line_end: 21
  - "@id": function:ec003dfa
    "@type": function
    title: getNthColumn
    description: Function getNthColumn(n) in
      packages/spec-parser-lib/coverage/lcov-report/sorter.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/sorter.js
    name: getNthColumn
    signature: getNthColumn(n)
    lang: javascript
    line_start: 23
    line_end: 25
  - "@id": function:05c8cc35
    "@type": function
    title: onFilterInput
    description: Function onFilterInput() in
      packages/spec-parser-lib/coverage/lcov-report/sorter.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/sorter.js
    name: onFilterInput
    signature: onFilterInput()
    lang: javascript
    line_start: 27
    line_end: 42
  - "@id": function:0f8f84f7
    "@type": function
    title: addSearchBox
    description: Function addSearchBox() in
      packages/spec-parser-lib/coverage/lcov-report/sorter.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/sorter.js
    name: addSearchBox
    signature: addSearchBox()
    lang: javascript
    line_start: 45
    line_end: 50
  - "@id": function:57c51601
    "@type": function
    title: loadColumns
    description: Function loadColumns() in
      packages/spec-parser-lib/coverage/lcov-report/sorter.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/sorter.js
    name: loadColumns
    signature: loadColumns()
    lang: javascript
    line_start: 53
    line_end: 75
  - "@id": function:f71ed92a
    "@type": function
    title: loadRowData
    description: Function loadRowData(tableRow) in
      packages/spec-parser-lib/coverage/lcov-report/sorter.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/sorter.js
    name: loadRowData
    signature: loadRowData(tableRow)
    lang: javascript
    line_start: 78
    line_end: 95
  - "@id": function:b8d4498d
    "@type": function
    title: loadData
    description: Function loadData() in
      packages/spec-parser-lib/coverage/lcov-report/sorter.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/sorter.js
    name: loadData
    signature: loadData()
    lang: javascript
    line_start: 97
    line_end: 104
  - "@id": function:9a8c579d
    "@type": function
    title: sortByIndex
    description: Function sortByIndex(index, desc) in
      packages/spec-parser-lib/coverage/lcov-report/sorter.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/sorter.js
    name: sortByIndex
    signature: sortByIndex(index, desc)
    lang: javascript
    line_start: 106
    line_end: 135
  - "@id": function:34fa47ce
    "@type": function
    title: anonymous
    description: Function  in packages/spec-parser-lib/coverage/lcov-report/sorter.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 108
    line_end: 112
  - "@id": function:ffee236f
    "@type": function
    title: anonymous
    description: Function  in packages/spec-parser-lib/coverage/lcov-report/sorter.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 120
    line_end: 122
  - "@id": function:c579615c
    "@type": function
    title: removeSortIndicators
    description: Function removeSortIndicators() in
      packages/spec-parser-lib/coverage/lcov-report/sorter.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/sorter.js
    name: removeSortIndicators
    signature: removeSortIndicators()
    lang: javascript
    line_start: 137
    line_end: 143
  - "@id": function:9bd844b7
    "@type": function
    title: addSortIndicators
    description: Function addSortIndicators() in
      packages/spec-parser-lib/coverage/lcov-report/sorter.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/sorter.js
    name: addSortIndicators
    signature: addSortIndicators()
    lang: javascript
    line_start: 145
    line_end: 149
  - "@id": function:00351beb
    "@type": function
    title: enableUI
    description: Function enableUI() in
      packages/spec-parser-lib/coverage/lcov-report/sorter.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/sorter.js
    name: enableUI
    signature: enableUI()
    lang: javascript
    line_start: 151
    line_end: 182
  - "@id": function:907ceae7
    "@type": function
    title: anonymous
    description: Function  in packages/spec-parser-lib/coverage/lcov-report/sorter.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 154
    line_end: 169
  - "@id": function:a0814fa7
    "@type": function
    title: anonymous
    description: Function  in packages/spec-parser-lib/coverage/lcov-report/sorter.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 157
    line_end: 168
  - "@id": function:4d3dcec5
    "@type": function
    title: anonymous
    description: Function  in packages/spec-parser-lib/coverage/lcov-report/sorter.js
    filePath: packages/spec-parser-lib/coverage/lcov-report/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 184
    line_end: 193
  - "@id": function:08075c38
    "@type": function
    title: anonymous
    description: Function  in packages/spec-parser-lib/coverage/prettify.js
    filePath: packages/spec-parser-lib/coverage/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:bbb31c1b
    "@type": function
    title: k
    description: Function k(Z) in packages/spec-parser-lib/coverage/prettify.js
    filePath: packages/spec-parser-lib/coverage/prettify.js
    name: k
    signature: k(Z)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:2e9a32a1
    "@type": function
    title: ab
    description: Function ab(ah) in packages/spec-parser-lib/coverage/prettify.js
    filePath: packages/spec-parser-lib/coverage/prettify.js
    name: ab
    signature: ab(ah)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:cc300140
    "@type": function
    title: T
    description: Function T(af) in packages/spec-parser-lib/coverage/prettify.js
    filePath: packages/spec-parser-lib/coverage/prettify.js
    name: T
    signature: T(af)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:847a7794
    "@type": function
    title: X
    description: Function X(am) in packages/spec-parser-lib/coverage/prettify.js
    filePath: packages/spec-parser-lib/coverage/prettify.js
    name: X
    signature: X(am)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:08075c38
    "@type": function
    title: anonymous
    description: Function  in packages/spec-parser-lib/coverage/prettify.js
    filePath: packages/spec-parser-lib/coverage/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:649812e2
    "@type": function
    title: W
    description: Function W(al) in packages/spec-parser-lib/coverage/prettify.js
    filePath: packages/spec-parser-lib/coverage/prettify.js
    name: W
    signature: W(al)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:08075c38
    "@type": function
    title: anonymous
    description: Function  in packages/spec-parser-lib/coverage/prettify.js
    filePath: packages/spec-parser-lib/coverage/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:a72b08b0
    "@type": function
    title: a
    description: Function a(V) in packages/spec-parser-lib/coverage/prettify.js
    filePath: packages/spec-parser-lib/coverage/prettify.js
    name: a
    signature: a(V)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:d74735a4
    "@type": function
    title: aa
    description: Function aa(ab) in packages/spec-parser-lib/coverage/prettify.js
    filePath: packages/spec-parser-lib/coverage/prettify.js
    name: aa
    signature: aa(ab)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:346d5ede
    "@type": function
    title: B
    description: Function B(S,U,W,T) in packages/spec-parser-lib/coverage/prettify.js
    filePath: packages/spec-parser-lib/coverage/prettify.js
    name: B
    signature: B(S,U,W,T)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:8c29f4e3
    "@type": function
    title: o
    description: Function o(S) in packages/spec-parser-lib/coverage/prettify.js
    filePath: packages/spec-parser-lib/coverage/prettify.js
    name: o
    signature: o(S)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:775bc3a0
    "@type": function
    title: g
    description: Function g(U,T) in packages/spec-parser-lib/coverage/prettify.js
    filePath: packages/spec-parser-lib/coverage/prettify.js
    name: g
    signature: g(U,T)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:08075c38
    "@type": function
    title: anonymous
    description: Function  in packages/spec-parser-lib/coverage/prettify.js
    filePath: packages/spec-parser-lib/coverage/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:08075c38
    "@type": function
    title: anonymous
    description: Function  in packages/spec-parser-lib/coverage/prettify.js
    filePath: packages/spec-parser-lib/coverage/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:a51f741e
    "@type": function
    title: i
    description: Function i(T) in packages/spec-parser-lib/coverage/prettify.js
    filePath: packages/spec-parser-lib/coverage/prettify.js
    name: i
    signature: i(T)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:f82820e5
    "@type": function
    title: Q
    description: Function Q(V,ag) in packages/spec-parser-lib/coverage/prettify.js
    filePath: packages/spec-parser-lib/coverage/prettify.js
    name: Q
    signature: Q(V,ag)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:12790c3f
    "@type": function
    title: ae
    description: Function ae(al) in packages/spec-parser-lib/coverage/prettify.js
    filePath: packages/spec-parser-lib/coverage/prettify.js
    name: ae
    signature: ae(al)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:86dcb3d9
    "@type": function
    title: ad
    description: Function ad(ak) in packages/spec-parser-lib/coverage/prettify.js
    filePath: packages/spec-parser-lib/coverage/prettify.js
    name: ad
    signature: ad(ak)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:e0e82ce9
    "@type": function
    title: ai
    description: Function ai(al,ar) in packages/spec-parser-lib/coverage/prettify.js
    filePath: packages/spec-parser-lib/coverage/prettify.js
    name: ai
    signature: ai(al,ar)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:a5249331
    "@type": function
    title: D
    description: Function D(ac) in packages/spec-parser-lib/coverage/prettify.js
    filePath: packages/spec-parser-lib/coverage/prettify.js
    name: D
    signature: D(ac)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:8347b036
    "@type": function
    title: c
    description: Function c(U,V) in packages/spec-parser-lib/coverage/prettify.js
    filePath: packages/spec-parser-lib/coverage/prettify.js
    name: c
    signature: c(U,V)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:997fcf21
    "@type": function
    title: q
    description: Function q(T,S) in packages/spec-parser-lib/coverage/prettify.js
    filePath: packages/spec-parser-lib/coverage/prettify.js
    name: q
    signature: q(T,S)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:4d4d472b
    "@type": function
    title: d
    description: Function d(V) in packages/spec-parser-lib/coverage/prettify.js
    filePath: packages/spec-parser-lib/coverage/prettify.js
    name: d
    signature: d(V)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:0ac7754d
    "@type": function
    title: y
    description: Function y(W,V,U) in packages/spec-parser-lib/coverage/prettify.js
    filePath: packages/spec-parser-lib/coverage/prettify.js
    name: y
    signature: y(W,V,U)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:a96d29d0
    "@type": function
    title: b
    description: Function b(ad) in packages/spec-parser-lib/coverage/prettify.js
    filePath: packages/spec-parser-lib/coverage/prettify.js
    name: b
    signature: b(ad)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:f5802cc2
    "@type": function
    title: Y
    description: Function Y(af) in packages/spec-parser-lib/coverage/prettify.js
    filePath: packages/spec-parser-lib/coverage/prettify.js
    name: Y
    signature: Y(af)
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:08075c38
    "@type": function
    title: anonymous
    description: Function  in packages/spec-parser-lib/coverage/prettify.js
    filePath: packages/spec-parser-lib/coverage/prettify.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:21b65ca4
    "@type": function
    title: U
    description: Function U() in packages/spec-parser-lib/coverage/prettify.js
    filePath: packages/spec-parser-lib/coverage/prettify.js
    name: U
    signature: U()
    lang: javascript
    line_start: 2
    line_end: 2
  - "@id": function:dd482cff
    "@type": function
    title: anonymous
    description: Function  in packages/spec-parser-lib/coverage/sorter.js
    filePath: packages/spec-parser-lib/coverage/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 2
    line_end: 194
  - "@id": function:919eb5c0
    "@type": function
    title: getTable
    description: Function getTable() in packages/spec-parser-lib/coverage/sorter.js
    filePath: packages/spec-parser-lib/coverage/sorter.js
    name: getTable
    signature: getTable()
    lang: javascript
    line_start: 11
    line_end: 13
  - "@id": function:dd9c0116
    "@type": function
    title: getTableHeader
    description: Function getTableHeader() in packages/spec-parser-lib/coverage/sorter.js
    filePath: packages/spec-parser-lib/coverage/sorter.js
    name: getTableHeader
    signature: getTableHeader()
    lang: javascript
    line_start: 15
    line_end: 17
  - "@id": function:5d388440
    "@type": function
    title: getTableBody
    description: Function getTableBody() in packages/spec-parser-lib/coverage/sorter.js
    filePath: packages/spec-parser-lib/coverage/sorter.js
    name: getTableBody
    signature: getTableBody()
    lang: javascript
    line_start: 19
    line_end: 21
  - "@id": function:0ebdc532
    "@type": function
    title: getNthColumn
    description: Function getNthColumn(n) in packages/spec-parser-lib/coverage/sorter.js
    filePath: packages/spec-parser-lib/coverage/sorter.js
    name: getNthColumn
    signature: getNthColumn(n)
    lang: javascript
    line_start: 23
    line_end: 25
  - "@id": function:f46c407e
    "@type": function
    title: onFilterInput
    description: Function onFilterInput() in packages/spec-parser-lib/coverage/sorter.js
    filePath: packages/spec-parser-lib/coverage/sorter.js
    name: onFilterInput
    signature: onFilterInput()
    lang: javascript
    line_start: 27
    line_end: 42
  - "@id": function:e0534d25
    "@type": function
    title: addSearchBox
    description: Function addSearchBox() in packages/spec-parser-lib/coverage/sorter.js
    filePath: packages/spec-parser-lib/coverage/sorter.js
    name: addSearchBox
    signature: addSearchBox()
    lang: javascript
    line_start: 45
    line_end: 50
  - "@id": function:ec096734
    "@type": function
    title: loadColumns
    description: Function loadColumns() in packages/spec-parser-lib/coverage/sorter.js
    filePath: packages/spec-parser-lib/coverage/sorter.js
    name: loadColumns
    signature: loadColumns()
    lang: javascript
    line_start: 53
    line_end: 75
  - "@id": function:e0dc293b
    "@type": function
    title: loadRowData
    description: Function loadRowData(tableRow) in
      packages/spec-parser-lib/coverage/sorter.js
    filePath: packages/spec-parser-lib/coverage/sorter.js
    name: loadRowData
    signature: loadRowData(tableRow)
    lang: javascript
    line_start: 78
    line_end: 95
  - "@id": function:8980c5a0
    "@type": function
    title: loadData
    description: Function loadData() in packages/spec-parser-lib/coverage/sorter.js
    filePath: packages/spec-parser-lib/coverage/sorter.js
    name: loadData
    signature: loadData()
    lang: javascript
    line_start: 97
    line_end: 104
  - "@id": function:9b1fd61f
    "@type": function
    title: sortByIndex
    description: Function sortByIndex(index, desc) in
      packages/spec-parser-lib/coverage/sorter.js
    filePath: packages/spec-parser-lib/coverage/sorter.js
    name: sortByIndex
    signature: sortByIndex(index, desc)
    lang: javascript
    line_start: 106
    line_end: 135
  - "@id": function:87e4b43d
    "@type": function
    title: anonymous
    description: Function  in packages/spec-parser-lib/coverage/sorter.js
    filePath: packages/spec-parser-lib/coverage/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 108
    line_end: 112
  - "@id": function:54ac7ae5
    "@type": function
    title: anonymous
    description: Function  in packages/spec-parser-lib/coverage/sorter.js
    filePath: packages/spec-parser-lib/coverage/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 120
    line_end: 122
  - "@id": function:e1ea9dc4
    "@type": function
    title: removeSortIndicators
    description: Function removeSortIndicators() in
      packages/spec-parser-lib/coverage/sorter.js
    filePath: packages/spec-parser-lib/coverage/sorter.js
    name: removeSortIndicators
    signature: removeSortIndicators()
    lang: javascript
    line_start: 137
    line_end: 143
  - "@id": function:58266fe0
    "@type": function
    title: addSortIndicators
    description: Function addSortIndicators() in packages/spec-parser-lib/coverage/sorter.js
    filePath: packages/spec-parser-lib/coverage/sorter.js
    name: addSortIndicators
    signature: addSortIndicators()
    lang: javascript
    line_start: 145
    line_end: 149
  - "@id": function:f94a435c
    "@type": function
    title: enableUI
    description: Function enableUI() in packages/spec-parser-lib/coverage/sorter.js
    filePath: packages/spec-parser-lib/coverage/sorter.js
    name: enableUI
    signature: enableUI()
    lang: javascript
    line_start: 151
    line_end: 182
  - "@id": function:78d80482
    "@type": function
    title: anonymous
    description: Function  in packages/spec-parser-lib/coverage/sorter.js
    filePath: packages/spec-parser-lib/coverage/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 154
    line_end: 169
  - "@id": function:725dc16f
    "@type": function
    title: anonymous
    description: Function  in packages/spec-parser-lib/coverage/sorter.js
    filePath: packages/spec-parser-lib/coverage/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 157
    line_end: 168
  - "@id": function:4a5dccf8
    "@type": function
    title: anonymous
    description: Function  in packages/spec-parser-lib/coverage/sorter.js
    filePath: packages/spec-parser-lib/coverage/sorter.js
    name: anonymous
    signature: ""
    lang: javascript
    line_start: 184
    line_end: 193
  - "@type": workflow_calls
    source: function:cb5cdbdf
    target: function:cf760c3b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/block-navigation.js
    line: 32
  - "@type": workflow_calls
    source: function:cb5cdbdf
    target: function:4434cce9
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/block-navigation.js
    line: 49
  - "@type": workflow_calls
    source: function:cb5cdbdf
    target: function:4434cce9
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/block-navigation.js
    line: 62
  - "@type": workflow_calls
    source: function:cb5cdbdf
    target: function:78c9d6c0
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/block-navigation.js
    line: 77
  - "@type": workflow_calls
    source: function:cb5cdbdf
    target: function:e81de177
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/block-navigation.js
    line: 82
  - "@type": workflow_calls
    source: function:3da3400c
    target: function:ef6be38d
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/block-navigation.js
    line: 32
  - "@type": workflow_calls
    source: function:3da3400c
    target: function:a9e66fb0
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/block-navigation.js
    line: 49
  - "@type": workflow_calls
    source: function:3da3400c
    target: function:a9e66fb0
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/block-navigation.js
    line: 62
  - "@type": workflow_calls
    source: function:3da3400c
    target: function:536ea490
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/block-navigation.js
    line: 77
  - "@type": workflow_calls
    source: function:3da3400c
    target: function:f1b06ded
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/block-navigation.js
    line: 82
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:34306a9a
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:34306a9a
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:da14a863
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:da14a863
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:a2e0d725
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:a6c52439
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:eea6ec4a
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:eea6ec4a
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:a6c52439
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:dd0c7003
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:77bbcbed
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:77bbcbed
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:f70ad850
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:77bbcbed
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:0fa1db7c
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:022cc74f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:c53947e5
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:a80d8f35
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:c53947e5
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:5147793a
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:5147793a
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:a80d8f35
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:217434f9
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:217434f9
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:0fa1db7c
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:217434f9
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:0fa1db7c
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:217434f9
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:0fa1db7c
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:217434f9
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:022cc74f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:217434f9
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:022cc74f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:217434f9
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:022cc74f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:217434f9
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:022cc74f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:217434f9
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:022cc74f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:217434f9
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:022cc74f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:217434f9
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:022cc74f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:217434f9
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:022cc74f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:217434f9
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:022cc74f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:217434f9
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:022cc74f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:217434f9
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:0fa1db7c
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:43a35ed7
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:f70ad850
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:c8a18a28
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:cbfdd0ea
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:8543d000
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:c688bbd7
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:c688bbd7
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:c688bbd7
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:48824308
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:cbfdd0ea
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:8543d000
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:c53947e5
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:06a2db0a
    target: function:7f0bf11c
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:14bab3df
    target: function:1f2b82a2
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/sorter.js
    line: 16
  - "@type": workflow_calls
    source: function:14bab3df
    target: function:1f2b82a2
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/sorter.js
    line: 20
  - "@type": workflow_calls
    source: function:14bab3df
    target: function:ed0db8ce
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/sorter.js
    line: 24
  - "@type": workflow_calls
    source: function:14bab3df
    target: function:ed0db8ce
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/sorter.js
    line: 54
  - "@type": workflow_calls
    source: function:14bab3df
    target: function:04340385
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/sorter.js
    line: 98
  - "@type": workflow_calls
    source: function:14bab3df
    target: function:6bf8cf59
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/sorter.js
    line: 102
  - "@type": workflow_calls
    source: function:14bab3df
    target: function:09feb2d4
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/sorter.js
    line: 138
  - "@type": workflow_calls
    source: function:14bab3df
    target: function:09feb2d4
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/sorter.js
    line: 146
  - "@type": workflow_calls
    source: function:14bab3df
    target: function:b7625662
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/sorter.js
    line: 163
  - "@type": workflow_calls
    source: function:14bab3df
    target: function:5a167e80
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/sorter.js
    line: 164
  - "@type": workflow_calls
    source: function:14bab3df
    target: function:33bb6ceb
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/sorter.js
    line: 167
  - "@type": workflow_calls
    source: function:14bab3df
    target: function:09feb2d4
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/sorter.js
    line: 174
  - "@type": workflow_calls
    source: function:14bab3df
    target: function:1f2b82a2
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/sorter.js
    line: 185
  - "@type": workflow_calls
    source: function:14bab3df
    target: function:595ab877
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/sorter.js
    line: 188
  - "@type": workflow_calls
    source: function:14bab3df
    target: function:96298e85
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/sorter.js
    line: 189
  - "@type": workflow_calls
    source: function:14bab3df
    target: function:2ca6fca7
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/sorter.js
    line: 190
  - "@type": workflow_calls
    source: function:14bab3df
    target: function:33bb6ceb
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/sorter.js
    line: 191
  - "@type": workflow_calls
    source: function:14bab3df
    target: function:598a0b2e
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/lcov-report/sorter.js
    line: 192
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:6d96f7db
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:6d96f7db
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:aacb295a
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:aacb295a
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:c22cc098
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:85c472fb
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:06b964cb
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:06b964cb
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:85c472fb
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:9863e291
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:2c8a457e
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:2c8a457e
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:5eb71c32
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:2c8a457e
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:907e7c55
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:3c9268a1
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:77dd7299
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:fdaf44c2
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:77dd7299
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:0b266676
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:0b266676
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:fdaf44c2
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:88c45617
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:88c45617
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:907e7c55
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:88c45617
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:907e7c55
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:88c45617
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:907e7c55
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:88c45617
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:3c9268a1
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:88c45617
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:3c9268a1
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:88c45617
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:3c9268a1
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:88c45617
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:3c9268a1
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:88c45617
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:3c9268a1
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:88c45617
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:3c9268a1
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:88c45617
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:3c9268a1
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:88c45617
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:3c9268a1
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:88c45617
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:3c9268a1
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:88c45617
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:3c9268a1
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:88c45617
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:907e7c55
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:2badf74e
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:5eb71c32
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:e5e3789d
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:73c1938f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:4b5c9080
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:8d3ed4a3
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:8d3ed4a3
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:8d3ed4a3
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:ece06b95
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:73c1938f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:4b5c9080
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:77dd7299
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e524ba15
    target: function:4311f862
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:23b20237
    target: function:518cd5e2
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/sorter.js
    line: 16
  - "@type": workflow_calls
    source: function:23b20237
    target: function:518cd5e2
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/sorter.js
    line: 20
  - "@type": workflow_calls
    source: function:23b20237
    target: function:111d25a1
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/sorter.js
    line: 24
  - "@type": workflow_calls
    source: function:23b20237
    target: function:111d25a1
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/sorter.js
    line: 54
  - "@type": workflow_calls
    source: function:23b20237
    target: function:ce4084ce
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/sorter.js
    line: 98
  - "@type": workflow_calls
    source: function:23b20237
    target: function:e3f83c45
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/sorter.js
    line: 102
  - "@type": workflow_calls
    source: function:23b20237
    target: function:c342715f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/sorter.js
    line: 138
  - "@type": workflow_calls
    source: function:23b20237
    target: function:c342715f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/sorter.js
    line: 146
  - "@type": workflow_calls
    source: function:23b20237
    target: function:f533c0d6
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/sorter.js
    line: 163
  - "@type": workflow_calls
    source: function:23b20237
    target: function:39e09b2d
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/sorter.js
    line: 164
  - "@type": workflow_calls
    source: function:23b20237
    target: function:06d23133
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/sorter.js
    line: 167
  - "@type": workflow_calls
    source: function:23b20237
    target: function:c342715f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/sorter.js
    line: 174
  - "@type": workflow_calls
    source: function:23b20237
    target: function:518cd5e2
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/sorter.js
    line: 185
  - "@type": workflow_calls
    source: function:23b20237
    target: function:85ffcc62
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/sorter.js
    line: 188
  - "@type": workflow_calls
    source: function:23b20237
    target: function:dc04e9a8
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/sorter.js
    line: 189
  - "@type": workflow_calls
    source: function:23b20237
    target: function:7159af20
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/sorter.js
    line: 190
  - "@type": workflow_calls
    source: function:23b20237
    target: function:06d23133
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/sorter.js
    line: 191
  - "@type": workflow_calls
    source: function:23b20237
    target: function:e10449db
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/coverage/sorter.js
    line: 192
  - "@type": workflow_calls
    source: function:70e4492b
    target: function:3e3c815f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/tests/fixtures/javascript/hello.js
    line: 15
  - "@type": workflow_calls
    source: function:c831252e
    target: function:3e3c815f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/tests/fixtures/javascript/hello.js
    line: 23
  - "@type": workflow_calls
    source: function:cb75dd81
    target: function:d00970ac
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/tests/fixtures/javascript/hello.js
    line: 33
  - "@type": workflow_calls
    source: function:b5172ccc
    target: function:3e3c815f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/tests/fixtures/javascript/hello.js
    line: 39
  - "@type": workflow_calls
    source: function:4f3d35ee
    target: function:d00970ac
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/tests/fixtures/javascript/hello.js
    line: 47
  - "@type": workflow_calls
    source: function:4f3d35ee
    target: function:3e3c815f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/tests/fixtures/javascript/hello.js
    line: 48
  - "@type": workflow_calls
    source: function:4f3d35ee
    target: function:70e4492b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/tests/fixtures/javascript/hello.js
    line: 51
  - "@type": workflow_calls
    source: function:4f3d35ee
    target: function:c831252e
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/tests/fixtures/javascript/hello.js
    line: 58
  - "@type": workflow_calls
    source: function:d711b6cf
    target: function:d0c1ee84
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/tests/fixtures/python/hello.py
    line: 17
  - "@type": workflow_calls
    source: function:0133bd6b
    target: function:bfe7ae8a
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/tests/fixtures/python/hello.py
    line: 26
  - "@type": workflow_calls
    source: function:0133bd6b
    target: function:d0c1ee84
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/tests/fixtures/python/hello.py
    line: 27
  - "@type": workflow_calls
    source: function:0133bd6b
    target: function:d711b6cf
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/tests/fixtures/python/hello.py
    line: 30
  - "@type": workflow_calls
    source: function:0133bd6b
    target: function:d19cbd09
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/code-parser-lib/tests/fixtures/python/hello.py
    line: 33
  - "@type": workflow_calls
    source: function:89077f38
    target: function:181cf731
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/block-navigation.js
    line: 32
  - "@type": workflow_calls
    source: function:89077f38
    target: function:6f1f0c0b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/block-navigation.js
    line: 49
  - "@type": workflow_calls
    source: function:89077f38
    target: function:6f1f0c0b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/block-navigation.js
    line: 62
  - "@type": workflow_calls
    source: function:89077f38
    target: function:0cd408f8
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/block-navigation.js
    line: 77
  - "@type": workflow_calls
    source: function:89077f38
    target: function:1fe5819e
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/block-navigation.js
    line: 82
  - "@type": workflow_calls
    source: function:9c52c697
    target: function:2f219ac8
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/block-navigation.js
    line: 32
  - "@type": workflow_calls
    source: function:9c52c697
    target: function:c4425cda
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/block-navigation.js
    line: 49
  - "@type": workflow_calls
    source: function:9c52c697
    target: function:c4425cda
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/block-navigation.js
    line: 62
  - "@type": workflow_calls
    source: function:9c52c697
    target: function:45d2aefc
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/block-navigation.js
    line: 77
  - "@type": workflow_calls
    source: function:9c52c697
    target: function:20f0d49c
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/block-navigation.js
    line: 82
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:51038a40
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:51038a40
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:bb44441b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:bb44441b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:2ffec5e5
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:23a98c52
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:68ac86ff
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:68ac86ff
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:23a98c52
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:7e03d1e7
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:8ac21559
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:8ac21559
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:960271dd
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:8ac21559
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:bf8ae5f6
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:810ab81d
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:18e5ea15
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:fce37fbb
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:18e5ea15
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:1d8075af
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:1d8075af
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:fce37fbb
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:a05a3f1b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:a05a3f1b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:bf8ae5f6
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:a05a3f1b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:bf8ae5f6
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:a05a3f1b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:bf8ae5f6
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:a05a3f1b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:810ab81d
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:a05a3f1b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:810ab81d
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:a05a3f1b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:810ab81d
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:a05a3f1b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:810ab81d
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:a05a3f1b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:810ab81d
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:a05a3f1b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:810ab81d
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:a05a3f1b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:810ab81d
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:a05a3f1b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:810ab81d
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:a05a3f1b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:810ab81d
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:a05a3f1b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:810ab81d
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:a05a3f1b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:bf8ae5f6
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:38409e9c
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:960271dd
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:f38a9806
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:6863ebf5
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:1ecad89c
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:fe7e632b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:fe7e632b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:fe7e632b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:b1081e77
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:6863ebf5
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:1ecad89c
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:18e5ea15
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:faa7bf35
    target: function:ab5f1451
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:69ba4210
    target: function:96cc2c87
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/sorter.js
    line: 16
  - "@type": workflow_calls
    source: function:69ba4210
    target: function:96cc2c87
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/sorter.js
    line: 20
  - "@type": workflow_calls
    source: function:69ba4210
    target: function:8bfe4d5e
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/sorter.js
    line: 24
  - "@type": workflow_calls
    source: function:69ba4210
    target: function:8bfe4d5e
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/sorter.js
    line: 54
  - "@type": workflow_calls
    source: function:69ba4210
    target: function:7ab44ce1
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/sorter.js
    line: 98
  - "@type": workflow_calls
    source: function:69ba4210
    target: function:b94a3090
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/sorter.js
    line: 102
  - "@type": workflow_calls
    source: function:69ba4210
    target: function:4585cf6f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/sorter.js
    line: 138
  - "@type": workflow_calls
    source: function:69ba4210
    target: function:4585cf6f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/sorter.js
    line: 146
  - "@type": workflow_calls
    source: function:69ba4210
    target: function:8bb97a6f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/sorter.js
    line: 163
  - "@type": workflow_calls
    source: function:69ba4210
    target: function:dcdd79f7
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/sorter.js
    line: 164
  - "@type": workflow_calls
    source: function:69ba4210
    target: function:155f8747
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/sorter.js
    line: 167
  - "@type": workflow_calls
    source: function:69ba4210
    target: function:4585cf6f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/sorter.js
    line: 174
  - "@type": workflow_calls
    source: function:69ba4210
    target: function:96cc2c87
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/sorter.js
    line: 185
  - "@type": workflow_calls
    source: function:69ba4210
    target: function:ab753622
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/sorter.js
    line: 188
  - "@type": workflow_calls
    source: function:69ba4210
    target: function:7fcd8c48
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/sorter.js
    line: 189
  - "@type": workflow_calls
    source: function:69ba4210
    target: function:ecce2e28
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/sorter.js
    line: 190
  - "@type": workflow_calls
    source: function:69ba4210
    target: function:155f8747
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/sorter.js
    line: 191
  - "@type": workflow_calls
    source: function:69ba4210
    target: function:b456d0cf
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/lcov-report/sorter.js
    line: 192
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:eb2d1fab
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:eb2d1fab
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:611aa608
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:611aa608
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:94d2ade2
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:0e71f83e
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:262222d7
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:262222d7
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:0e71f83e
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:4f1512ba
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:979397a0
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:979397a0
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:50e7d45c
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:979397a0
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:b43e68f2
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:0daa86d3
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:35469f5e
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:99ff8069
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:35469f5e
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:e971d614
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:e971d614
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:99ff8069
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:b60afdb1
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:b60afdb1
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:b43e68f2
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:b60afdb1
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:b43e68f2
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:b60afdb1
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:b43e68f2
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:b60afdb1
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:0daa86d3
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:b60afdb1
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:0daa86d3
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:b60afdb1
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:0daa86d3
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:b60afdb1
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:0daa86d3
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:b60afdb1
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:0daa86d3
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:b60afdb1
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:0daa86d3
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:b60afdb1
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:0daa86d3
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:b60afdb1
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:0daa86d3
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:b60afdb1
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:0daa86d3
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:b60afdb1
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:0daa86d3
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:b60afdb1
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:b43e68f2
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:c8b44991
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:50e7d45c
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:bf11f81b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:f143643e
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:b1beca04
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:084d0bdf
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:084d0bdf
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:084d0bdf
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:96951bf5
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:f143643e
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:b1beca04
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:35469f5e
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:4d951c43
    target: function:469f4a4d
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f35d5879
    target: function:62fd7799
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/sorter.js
    line: 16
  - "@type": workflow_calls
    source: function:f35d5879
    target: function:62fd7799
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/sorter.js
    line: 20
  - "@type": workflow_calls
    source: function:f35d5879
    target: function:9b39335e
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/sorter.js
    line: 24
  - "@type": workflow_calls
    source: function:f35d5879
    target: function:9b39335e
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/sorter.js
    line: 54
  - "@type": workflow_calls
    source: function:f35d5879
    target: function:5acfcbec
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/sorter.js
    line: 98
  - "@type": workflow_calls
    source: function:f35d5879
    target: function:6f0bbe4a
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/sorter.js
    line: 102
  - "@type": workflow_calls
    source: function:f35d5879
    target: function:ec34b45b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/sorter.js
    line: 138
  - "@type": workflow_calls
    source: function:f35d5879
    target: function:ec34b45b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/sorter.js
    line: 146
  - "@type": workflow_calls
    source: function:f35d5879
    target: function:2bf48c07
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/sorter.js
    line: 163
  - "@type": workflow_calls
    source: function:f35d5879
    target: function:490adf81
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/sorter.js
    line: 164
  - "@type": workflow_calls
    source: function:f35d5879
    target: function:5cf0369a
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/sorter.js
    line: 167
  - "@type": workflow_calls
    source: function:f35d5879
    target: function:ec34b45b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/sorter.js
    line: 174
  - "@type": workflow_calls
    source: function:f35d5879
    target: function:62fd7799
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/sorter.js
    line: 185
  - "@type": workflow_calls
    source: function:f35d5879
    target: function:aec163de
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/sorter.js
    line: 188
  - "@type": workflow_calls
    source: function:f35d5879
    target: function:7b1a972e
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/sorter.js
    line: 189
  - "@type": workflow_calls
    source: function:f35d5879
    target: function:77a7d51a
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/sorter.js
    line: 190
  - "@type": workflow_calls
    source: function:f35d5879
    target: function:5cf0369a
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/sorter.js
    line: 191
  - "@type": workflow_calls
    source: function:f35d5879
    target: function:7e25f937
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-cli/coverage/sorter.js
    line: 192
  - "@type": workflow_calls
    source: function:27973ef1
    target: function:f8b68537
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/block-navigation.js
    line: 32
  - "@type": workflow_calls
    source: function:27973ef1
    target: function:90343e73
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/block-navigation.js
    line: 49
  - "@type": workflow_calls
    source: function:27973ef1
    target: function:90343e73
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/block-navigation.js
    line: 62
  - "@type": workflow_calls
    source: function:27973ef1
    target: function:613e1073
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/block-navigation.js
    line: 77
  - "@type": workflow_calls
    source: function:27973ef1
    target: function:f01f626d
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/block-navigation.js
    line: 82
  - "@type": workflow_calls
    source: function:9603f228
    target: function:e791b486
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/block-navigation.js
    line: 32
  - "@type": workflow_calls
    source: function:9603f228
    target: function:f271d6ad
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/block-navigation.js
    line: 49
  - "@type": workflow_calls
    source: function:9603f228
    target: function:f271d6ad
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/block-navigation.js
    line: 62
  - "@type": workflow_calls
    source: function:9603f228
    target: function:9e96d8fd
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/block-navigation.js
    line: 77
  - "@type": workflow_calls
    source: function:9603f228
    target: function:3f89bbca
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/block-navigation.js
    line: 82
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:a8808683
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:a8808683
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:a8da3cba
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:a8da3cba
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:e18c678b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:e8c8ef11
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:28f864ac
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:28f864ac
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:e8c8ef11
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:ae3c8e5d
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:92ce3a4c
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:92ce3a4c
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:a42457fb
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:92ce3a4c
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:bfc3bcce
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:4cad8dad
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:646b9caa
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:3ff61363
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:646b9caa
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:2a1e0b88
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:2a1e0b88
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:3ff61363
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:67154efa
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:67154efa
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:bfc3bcce
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:67154efa
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:bfc3bcce
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:67154efa
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:bfc3bcce
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:67154efa
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:4cad8dad
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:67154efa
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:4cad8dad
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:67154efa
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:4cad8dad
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:67154efa
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:4cad8dad
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:67154efa
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:4cad8dad
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:67154efa
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:4cad8dad
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:67154efa
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:4cad8dad
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:67154efa
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:4cad8dad
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:67154efa
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:4cad8dad
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:67154efa
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:4cad8dad
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:67154efa
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:bfc3bcce
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:b1d6d08a
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:a42457fb
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:2713f478
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:c7f1bf3b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:5212edee
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:51eec226
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:51eec226
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:51eec226
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:b9cada14
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:c7f1bf3b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:5212edee
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:646b9caa
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f1e13714
    target: function:70fe7769
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:38f6219b
    target: function:6cb5abaf
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/sorter.js
    line: 16
  - "@type": workflow_calls
    source: function:38f6219b
    target: function:6cb5abaf
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/sorter.js
    line: 20
  - "@type": workflow_calls
    source: function:38f6219b
    target: function:6b2e20a6
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/sorter.js
    line: 24
  - "@type": workflow_calls
    source: function:38f6219b
    target: function:6b2e20a6
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/sorter.js
    line: 54
  - "@type": workflow_calls
    source: function:38f6219b
    target: function:d4d761d8
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/sorter.js
    line: 98
  - "@type": workflow_calls
    source: function:38f6219b
    target: function:c1912697
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/sorter.js
    line: 102
  - "@type": workflow_calls
    source: function:38f6219b
    target: function:947ab0bf
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/sorter.js
    line: 138
  - "@type": workflow_calls
    source: function:38f6219b
    target: function:947ab0bf
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/sorter.js
    line: 146
  - "@type": workflow_calls
    source: function:38f6219b
    target: function:1dcce6d2
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/sorter.js
    line: 163
  - "@type": workflow_calls
    source: function:38f6219b
    target: function:9a7dbbd8
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/sorter.js
    line: 164
  - "@type": workflow_calls
    source: function:38f6219b
    target: function:eed649c4
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/sorter.js
    line: 167
  - "@type": workflow_calls
    source: function:38f6219b
    target: function:947ab0bf
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/sorter.js
    line: 174
  - "@type": workflow_calls
    source: function:38f6219b
    target: function:6cb5abaf
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/sorter.js
    line: 185
  - "@type": workflow_calls
    source: function:38f6219b
    target: function:5157857e
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/sorter.js
    line: 188
  - "@type": workflow_calls
    source: function:38f6219b
    target: function:aaacd7f9
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/sorter.js
    line: 189
  - "@type": workflow_calls
    source: function:38f6219b
    target: function:c5507e9b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/sorter.js
    line: 190
  - "@type": workflow_calls
    source: function:38f6219b
    target: function:eed649c4
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/sorter.js
    line: 191
  - "@type": workflow_calls
    source: function:38f6219b
    target: function:b1ba1402
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/lcov-report/sorter.js
    line: 192
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:9780da34
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:9780da34
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:06a371e2
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:06a371e2
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:fc9ff684
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:d51ada8f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:10e515b9
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:10e515b9
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:d51ada8f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:fa841b33
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:0877d5d9
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:0877d5d9
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:6cc98df5
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:0877d5d9
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:cbfa0c28
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:02bc5d55
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:efff3355
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:33287b43
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:efff3355
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:0b41ed0d
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:0b41ed0d
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:33287b43
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:ec069283
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:ec069283
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:cbfa0c28
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:ec069283
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:cbfa0c28
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:ec069283
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:cbfa0c28
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:ec069283
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:02bc5d55
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:ec069283
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:02bc5d55
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:ec069283
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:02bc5d55
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:ec069283
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:02bc5d55
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:ec069283
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:02bc5d55
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:ec069283
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:02bc5d55
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:ec069283
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:02bc5d55
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:ec069283
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:02bc5d55
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:ec069283
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:02bc5d55
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:ec069283
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:02bc5d55
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:ec069283
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:cbfa0c28
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:f871a74a
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:6cc98df5
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:8cc7ce3f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:af37fe28
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:8b5c3393
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:062cfaa4
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:062cfaa4
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:062cfaa4
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:aa162a1b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:af37fe28
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:8b5c3393
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:efff3355
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:ca845fc1
    target: function:c37b5bfa
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:3f5c6d08
    target: function:26142384
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/sorter.js
    line: 16
  - "@type": workflow_calls
    source: function:3f5c6d08
    target: function:26142384
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/sorter.js
    line: 20
  - "@type": workflow_calls
    source: function:3f5c6d08
    target: function:e216d051
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/sorter.js
    line: 24
  - "@type": workflow_calls
    source: function:3f5c6d08
    target: function:e216d051
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/sorter.js
    line: 54
  - "@type": workflow_calls
    source: function:3f5c6d08
    target: function:db627f03
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/sorter.js
    line: 98
  - "@type": workflow_calls
    source: function:3f5c6d08
    target: function:9b3367b6
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/sorter.js
    line: 102
  - "@type": workflow_calls
    source: function:3f5c6d08
    target: function:4f65d303
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/sorter.js
    line: 138
  - "@type": workflow_calls
    source: function:3f5c6d08
    target: function:4f65d303
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/sorter.js
    line: 146
  - "@type": workflow_calls
    source: function:3f5c6d08
    target: function:2d37ae79
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/sorter.js
    line: 163
  - "@type": workflow_calls
    source: function:3f5c6d08
    target: function:21abb80f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/sorter.js
    line: 164
  - "@type": workflow_calls
    source: function:3f5c6d08
    target: function:e7ac8d20
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/sorter.js
    line: 167
  - "@type": workflow_calls
    source: function:3f5c6d08
    target: function:4f65d303
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/sorter.js
    line: 174
  - "@type": workflow_calls
    source: function:3f5c6d08
    target: function:26142384
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/sorter.js
    line: 185
  - "@type": workflow_calls
    source: function:3f5c6d08
    target: function:1dbb889c
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/sorter.js
    line: 188
  - "@type": workflow_calls
    source: function:3f5c6d08
    target: function:4e5e4a1f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/sorter.js
    line: 189
  - "@type": workflow_calls
    source: function:3f5c6d08
    target: function:8694ab8d
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/sorter.js
    line: 190
  - "@type": workflow_calls
    source: function:3f5c6d08
    target: function:e7ac8d20
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/sorter.js
    line: 191
  - "@type": workflow_calls
    source: function:3f5c6d08
    target: function:5c362229
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/kg-sync-lib/coverage/sorter.js
    line: 192
  - "@type": workflow_calls
    source: function:08d867c4
    target: function:81dc8786
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/block-navigation.js
    line: 32
  - "@type": workflow_calls
    source: function:08d867c4
    target: function:cee40c1a
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/block-navigation.js
    line: 49
  - "@type": workflow_calls
    source: function:08d867c4
    target: function:cee40c1a
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/block-navigation.js
    line: 62
  - "@type": workflow_calls
    source: function:08d867c4
    target: function:2f64c69c
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/block-navigation.js
    line: 77
  - "@type": workflow_calls
    source: function:08d867c4
    target: function:e20d15fe
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/block-navigation.js
    line: 82
  - "@type": workflow_calls
    source: function:bbbf3cca
    target: function:93530fdd
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/block-navigation.js
    line: 32
  - "@type": workflow_calls
    source: function:bbbf3cca
    target: function:4c1e1bc8
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/block-navigation.js
    line: 49
  - "@type": workflow_calls
    source: function:bbbf3cca
    target: function:4c1e1bc8
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/block-navigation.js
    line: 62
  - "@type": workflow_calls
    source: function:bbbf3cca
    target: function:6d68fe53
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/block-navigation.js
    line: 77
  - "@type": workflow_calls
    source: function:bbbf3cca
    target: function:a6eb4585
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/block-navigation.js
    line: 82
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:c0c03c5a
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:c0c03c5a
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:ae6cbeba
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:ae6cbeba
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:3c3b0983
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:b2bcc7d4
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:23b3ef83
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:23b3ef83
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:b2bcc7d4
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:c68242cf
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:71669fa8
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:71669fa8
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:6df15a39
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:71669fa8
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:53254329
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:31cd6fc5
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:85523f16
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:4ae2280a
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:85523f16
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:ba52438d
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:ba52438d
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:4ae2280a
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:3440670f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:3440670f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:53254329
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:3440670f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:53254329
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:3440670f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:53254329
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:3440670f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:31cd6fc5
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:3440670f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:31cd6fc5
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:3440670f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:31cd6fc5
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:3440670f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:31cd6fc5
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:3440670f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:31cd6fc5
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:3440670f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:31cd6fc5
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:3440670f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:31cd6fc5
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:3440670f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:31cd6fc5
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:3440670f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:31cd6fc5
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:3440670f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:31cd6fc5
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:3440670f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:53254329
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:6161b682
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:6df15a39
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:ea948fbf
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:47060299
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:b1c2b939
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:765b04bd
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:765b04bd
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:765b04bd
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:40c3ad3e
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:47060299
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:b1c2b939
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:85523f16
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:8fee06e7
    target: function:6f16fc06
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:c036e449
    target: function:a5e63525
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/sorter.js
    line: 16
  - "@type": workflow_calls
    source: function:c036e449
    target: function:a5e63525
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/sorter.js
    line: 20
  - "@type": workflow_calls
    source: function:c036e449
    target: function:a3593337
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/sorter.js
    line: 24
  - "@type": workflow_calls
    source: function:c036e449
    target: function:a3593337
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/sorter.js
    line: 54
  - "@type": workflow_calls
    source: function:c036e449
    target: function:e91e356d
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/sorter.js
    line: 98
  - "@type": workflow_calls
    source: function:c036e449
    target: function:06cd648c
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/sorter.js
    line: 102
  - "@type": workflow_calls
    source: function:c036e449
    target: function:dc030dfc
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/sorter.js
    line: 138
  - "@type": workflow_calls
    source: function:c036e449
    target: function:dc030dfc
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/sorter.js
    line: 146
  - "@type": workflow_calls
    source: function:c036e449
    target: function:38a45f42
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/sorter.js
    line: 163
  - "@type": workflow_calls
    source: function:c036e449
    target: function:870b9184
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/sorter.js
    line: 164
  - "@type": workflow_calls
    source: function:c036e449
    target: function:cf770f1b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/sorter.js
    line: 167
  - "@type": workflow_calls
    source: function:c036e449
    target: function:dc030dfc
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/sorter.js
    line: 174
  - "@type": workflow_calls
    source: function:c036e449
    target: function:a5e63525
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/sorter.js
    line: 185
  - "@type": workflow_calls
    source: function:c036e449
    target: function:6ea423a8
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/sorter.js
    line: 188
  - "@type": workflow_calls
    source: function:c036e449
    target: function:b52ed5d0
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/sorter.js
    line: 189
  - "@type": workflow_calls
    source: function:c036e449
    target: function:e00e9013
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/sorter.js
    line: 190
  - "@type": workflow_calls
    source: function:c036e449
    target: function:cf770f1b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/sorter.js
    line: 191
  - "@type": workflow_calls
    source: function:c036e449
    target: function:8f837732
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/lcov-report/sorter.js
    line: 192
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:06efc2ce
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:06efc2ce
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:e641795c
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:e641795c
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:9a4ad8d2
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:44e969ab
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:978ed439
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:978ed439
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:44e969ab
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:22c27bd3
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:04f0a48a
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:04f0a48a
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:870b7a0a
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:04f0a48a
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:ca14ff29
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:00ceff5c
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:09af3ccb
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:9c6676c3
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:09af3ccb
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:44b2e4d7
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:44b2e4d7
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:9c6676c3
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:d5d1625b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:d5d1625b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:ca14ff29
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:d5d1625b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:ca14ff29
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:d5d1625b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:ca14ff29
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:d5d1625b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:00ceff5c
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:d5d1625b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:00ceff5c
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:d5d1625b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:00ceff5c
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:d5d1625b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:00ceff5c
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:d5d1625b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:00ceff5c
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:d5d1625b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:00ceff5c
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:d5d1625b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:00ceff5c
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:d5d1625b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:00ceff5c
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:d5d1625b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:00ceff5c
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:d5d1625b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:00ceff5c
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:d5d1625b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:ca14ff29
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:dd8a57d8
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:870b7a0a
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:b016ffd3
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:23f085ab
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:8487054c
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:77c9804a
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:77c9804a
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:77c9804a
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:b8856c43
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:23f085ab
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:8487054c
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:09af3ccb
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:b05a869a
    target: function:5f63fed1
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:6f1c5b46
    target: function:2927f223
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/sorter.js
    line: 16
  - "@type": workflow_calls
    source: function:6f1c5b46
    target: function:2927f223
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/sorter.js
    line: 20
  - "@type": workflow_calls
    source: function:6f1c5b46
    target: function:31b63fdf
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/sorter.js
    line: 24
  - "@type": workflow_calls
    source: function:6f1c5b46
    target: function:31b63fdf
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/sorter.js
    line: 54
  - "@type": workflow_calls
    source: function:6f1c5b46
    target: function:5a956a6c
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/sorter.js
    line: 98
  - "@type": workflow_calls
    source: function:6f1c5b46
    target: function:c5dbc766
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/sorter.js
    line: 102
  - "@type": workflow_calls
    source: function:6f1c5b46
    target: function:9c81fed6
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/sorter.js
    line: 138
  - "@type": workflow_calls
    source: function:6f1c5b46
    target: function:9c81fed6
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/sorter.js
    line: 146
  - "@type": workflow_calls
    source: function:6f1c5b46
    target: function:ca012081
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/sorter.js
    line: 163
  - "@type": workflow_calls
    source: function:6f1c5b46
    target: function:41c09702
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/sorter.js
    line: 164
  - "@type": workflow_calls
    source: function:6f1c5b46
    target: function:3bdec215
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/sorter.js
    line: 167
  - "@type": workflow_calls
    source: function:6f1c5b46
    target: function:9c81fed6
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/sorter.js
    line: 174
  - "@type": workflow_calls
    source: function:6f1c5b46
    target: function:2927f223
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/sorter.js
    line: 185
  - "@type": workflow_calls
    source: function:6f1c5b46
    target: function:095fecfa
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/sorter.js
    line: 188
  - "@type": workflow_calls
    source: function:6f1c5b46
    target: function:d0197fa0
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/sorter.js
    line: 189
  - "@type": workflow_calls
    source: function:6f1c5b46
    target: function:56ad7396
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/sorter.js
    line: 190
  - "@type": workflow_calls
    source: function:6f1c5b46
    target: function:3bdec215
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/sorter.js
    line: 191
  - "@type": workflow_calls
    source: function:6f1c5b46
    target: function:c16f4d38
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/shared/coverage/sorter.js
    line: 192
  - "@type": workflow_calls
    source: function:e3148f75
    target: function:9daaf9b0
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/block-navigation.js
    line: 32
  - "@type": workflow_calls
    source: function:e3148f75
    target: function:b03003b9
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/block-navigation.js
    line: 49
  - "@type": workflow_calls
    source: function:e3148f75
    target: function:b03003b9
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/block-navigation.js
    line: 62
  - "@type": workflow_calls
    source: function:e3148f75
    target: function:a84805a0
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/block-navigation.js
    line: 77
  - "@type": workflow_calls
    source: function:e3148f75
    target: function:9952d843
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/block-navigation.js
    line: 82
  - "@type": workflow_calls
    source: function:1b61c8d6
    target: function:108d55ad
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/block-navigation.js
    line: 32
  - "@type": workflow_calls
    source: function:1b61c8d6
    target: function:be0a91f1
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/block-navigation.js
    line: 49
  - "@type": workflow_calls
    source: function:1b61c8d6
    target: function:be0a91f1
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/block-navigation.js
    line: 62
  - "@type": workflow_calls
    source: function:1b61c8d6
    target: function:0be8436d
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/block-navigation.js
    line: 77
  - "@type": workflow_calls
    source: function:1b61c8d6
    target: function:9adda11b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/block-navigation.js
    line: 82
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:8589b1ac
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:8589b1ac
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:f9f64d21
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:f9f64d21
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:1448d077
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:11494c53
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:3e72bfd7
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:3e72bfd7
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:11494c53
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:28028026
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:4c7b6caf
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:4c7b6caf
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:0834be98
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:4c7b6caf
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:cf1947ed
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:a70179af
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:0eb7c907
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:e97d8ed6
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:0eb7c907
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:d3f6320a
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:d3f6320a
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:e97d8ed6
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:d4df7d9f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:d4df7d9f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:cf1947ed
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:d4df7d9f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:cf1947ed
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:d4df7d9f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:cf1947ed
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:d4df7d9f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:a70179af
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:d4df7d9f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:a70179af
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:d4df7d9f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:a70179af
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:d4df7d9f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:a70179af
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:d4df7d9f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:a70179af
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:d4df7d9f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:a70179af
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:d4df7d9f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:a70179af
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:d4df7d9f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:a70179af
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:d4df7d9f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:a70179af
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:d4df7d9f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:a70179af
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:d4df7d9f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:cf1947ed
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:e0b6bb52
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:0834be98
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:452cbe79
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:978ebd1b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:90208730
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:40064b57
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:40064b57
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:40064b57
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:09ec2eca
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:978ebd1b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:90208730
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:0eb7c907
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:f73c9d8c
    target: function:6bbaa78e
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:e1bfa01c
    target: function:49eebae2
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/sorter.js
    line: 16
  - "@type": workflow_calls
    source: function:e1bfa01c
    target: function:49eebae2
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/sorter.js
    line: 20
  - "@type": workflow_calls
    source: function:e1bfa01c
    target: function:0666bdb0
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/sorter.js
    line: 24
  - "@type": workflow_calls
    source: function:e1bfa01c
    target: function:0666bdb0
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/sorter.js
    line: 54
  - "@type": workflow_calls
    source: function:e1bfa01c
    target: function:8cfe4e37
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/sorter.js
    line: 98
  - "@type": workflow_calls
    source: function:e1bfa01c
    target: function:f71ed92a
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/sorter.js
    line: 102
  - "@type": workflow_calls
    source: function:e1bfa01c
    target: function:ec003dfa
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/sorter.js
    line: 138
  - "@type": workflow_calls
    source: function:e1bfa01c
    target: function:ec003dfa
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/sorter.js
    line: 146
  - "@type": workflow_calls
    source: function:e1bfa01c
    target: function:9a8c579d
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/sorter.js
    line: 163
  - "@type": workflow_calls
    source: function:e1bfa01c
    target: function:c579615c
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/sorter.js
    line: 164
  - "@type": workflow_calls
    source: function:e1bfa01c
    target: function:9bd844b7
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/sorter.js
    line: 167
  - "@type": workflow_calls
    source: function:e1bfa01c
    target: function:ec003dfa
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/sorter.js
    line: 174
  - "@type": workflow_calls
    source: function:e1bfa01c
    target: function:49eebae2
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/sorter.js
    line: 185
  - "@type": workflow_calls
    source: function:e1bfa01c
    target: function:57c51601
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/sorter.js
    line: 188
  - "@type": workflow_calls
    source: function:e1bfa01c
    target: function:b8d4498d
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/sorter.js
    line: 189
  - "@type": workflow_calls
    source: function:e1bfa01c
    target: function:0f8f84f7
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/sorter.js
    line: 190
  - "@type": workflow_calls
    source: function:e1bfa01c
    target: function:9bd844b7
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/sorter.js
    line: 191
  - "@type": workflow_calls
    source: function:e1bfa01c
    target: function:00351beb
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/lcov-report/sorter.js
    line: 192
  - "@type": workflow_calls
    source: function:08075c38
    target: function:2e9a32a1
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:2e9a32a1
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:cc300140
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:cc300140
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:847a7794
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:649812e2
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:d74735a4
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:d74735a4
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:649812e2
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:bbb31c1b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:346d5ede
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:346d5ede
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:997fcf21
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:346d5ede
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:775bc3a0
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:a51f741e
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:86dcb3d9
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:12790c3f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:86dcb3d9
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:e0e82ce9
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:e0e82ce9
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:12790c3f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:8347b036
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:8347b036
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:775bc3a0
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:8347b036
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:775bc3a0
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:8347b036
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:775bc3a0
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:8347b036
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:a51f741e
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:8347b036
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:a51f741e
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:8347b036
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:a51f741e
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:8347b036
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:a51f741e
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:8347b036
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:a51f741e
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:8347b036
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:a51f741e
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:8347b036
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:a51f741e
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:8347b036
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:a51f741e
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:8347b036
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:a51f741e
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:8347b036
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:a51f741e
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:8347b036
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:775bc3a0
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:a72b08b0
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:997fcf21
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:a5249331
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:f82820e5
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:4d4d472b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:f5802cc2
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:f5802cc2
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:f5802cc2
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:8c29f4e3
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:f82820e5
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:4d4d472b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:86dcb3d9
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:08075c38
    target: function:21b65ca4
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/prettify.js
    line: 2
  - "@type": workflow_calls
    source: function:dd482cff
    target: function:919eb5c0
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/sorter.js
    line: 16
  - "@type": workflow_calls
    source: function:dd482cff
    target: function:919eb5c0
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/sorter.js
    line: 20
  - "@type": workflow_calls
    source: function:dd482cff
    target: function:dd9c0116
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/sorter.js
    line: 24
  - "@type": workflow_calls
    source: function:dd482cff
    target: function:dd9c0116
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/sorter.js
    line: 54
  - "@type": workflow_calls
    source: function:dd482cff
    target: function:5d388440
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/sorter.js
    line: 98
  - "@type": workflow_calls
    source: function:dd482cff
    target: function:e0dc293b
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/sorter.js
    line: 102
  - "@type": workflow_calls
    source: function:dd482cff
    target: function:0ebdc532
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/sorter.js
    line: 138
  - "@type": workflow_calls
    source: function:dd482cff
    target: function:0ebdc532
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/sorter.js
    line: 146
  - "@type": workflow_calls
    source: function:dd482cff
    target: function:9b1fd61f
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/sorter.js
    line: 163
  - "@type": workflow_calls
    source: function:dd482cff
    target: function:e1ea9dc4
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/sorter.js
    line: 164
  - "@type": workflow_calls
    source: function:dd482cff
    target: function:58266fe0
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/sorter.js
    line: 167
  - "@type": workflow_calls
    source: function:dd482cff
    target: function:0ebdc532
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/sorter.js
    line: 174
  - "@type": workflow_calls
    source: function:dd482cff
    target: function:919eb5c0
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/sorter.js
    line: 185
  - "@type": workflow_calls
    source: function:dd482cff
    target: function:ec096734
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/sorter.js
    line: 188
  - "@type": workflow_calls
    source: function:dd482cff
    target: function:8980c5a0
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/sorter.js
    line: 189
  - "@type": workflow_calls
    source: function:dd482cff
    target: function:e0534d25
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/sorter.js
    line: 190
  - "@type": workflow_calls
    source: function:dd482cff
    target: function:58266fe0
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/sorter.js
    line: 191
  - "@type": workflow_calls
    source: function:dd482cff
    target: function:f94a435c
    relationship: calls
    call_type: direct
    confidence: 1
    file: packages/spec-parser-lib/coverage/sorter.js
    line: 192
